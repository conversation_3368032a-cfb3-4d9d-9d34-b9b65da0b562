# Loki Log Aggregation Guide

## Quick Start

1. **Start the Loki stack:**
```bash
docker-compose -f docker-compose.yml -f docker-compose.loki.yml up -d
```

2. **Access Grafana:**
   - URL: http://localhost:3001
   - Username: admin
   - Password: omotesamba

3. **Navigate to the pre-configured dashboard:**
   - Go to Dashboards → Omotesamba Logs Dashboard
   - Or explore logs directly: Explore → Select "Loki" datasource

## Useful LogQL Queries

### 1. View All Errors Across Services
```logql
{compose_project="omotesamba"} |~ "(?i)(error|exception|failed|failure)"
```

### 2. Errors by Specific Service
```logql
{compose_service="pdf-extractor"} |~ "(?i)error"
```

### 3. Categorize Errors by Type
```logql
{compose_project="omotesamba"} 
|~ "(?i)error" 
| pattern "<_> Error: <error_type> <_>"
```

### 4. Monitor Queue Depths
```logql
{compose_service=~"queue-monitor|pipeline-validator"} 
|~ "Queue" 
| pattern "Queue <queue_name>: <queue_depth> items"
```

### 5. Track PDF Extraction Success Rate
```logql
sum(rate({compose_service="pdf-extractor"} |~ "successfully extracted" [5m])) / 
sum(rate({compose_service="pdf-extractor"} |~ "(successfully extracted|extraction failed)" [5m]))
```

### 6. Find Slow Processing Documents
```logql
{compose_service=~"categorizer|pro-analyzer|market-analyzer"} 
|~ "Processing time:" 
| pattern "Processing time: <time>ms" 
| time > 5000
```

### 7. Monitor Memory/Resource Errors
```logql
{compose_project="omotesamba"} |~ "(?i)(out of memory|oom|memory error|cuda.*memory)"
```

### 8. Track Document Flow Through Pipeline
```logql
{compose_project="omotesamba"} |~ "document_id_here"
```

### 9. Aggregate Errors by Hour
```logql
sum by (compose_service) (
  count_over_time(
    {compose_project="omotesamba"} |~ "(?i)error" [1h]
  )
)
```

### 10. Find Stuck Documents (Processing > 10 min)
```logql
{compose_project="omotesamba"} 
|~ "Processing document" 
| pattern "Processing document <doc_id>" 
!~ "completed|failed" [10m]
```

## Advanced Features

### Creating Alerts

1. Go to Alerting → Alert rules
2. Create new alert rule with query like:
```logql
sum(rate({compose_project="omotesamba"} |~ "(?i)error" [5m])) > 0.1
```

### Using Labels for Filtering

All containers are automatically labeled with:
- `compose_project`: Always "omotesamba"
- `compose_service`: Service name (e.g., "pdf-extractor", "categorizer")
- `container_name`: Full container name
- `level`: Log level (when detected)

### Performance Monitoring

Track processing performance:
```logql
avg_over_time(
  {compose_service="pdf-extractor"} 
  |~ "Processing time" 
  | regexp "Processing time: (?P<duration>\\d+)ms" 
  | unwrap duration [5m]
)
```

### Correlation with Metrics

You can correlate logs with Prometheus metrics by time:
1. Find error spike in logs
2. Check corresponding metrics in Prometheus/Grafana
3. Use the same time range to investigate

## Troubleshooting

### If logs aren't appearing:

1. Check Promtail is running:
```bash
docker logs omotesamba-promtail
```

2. Verify Docker labels:
```bash
docker inspect <container_name> | grep -A 10 Labels
```

3. Test Loki connection:
```bash
curl http://localhost:3100/ready
```

### Common Issues:

- **"too many outstanding requests"**: Reduce query time range
- **"maximum of series reached"**: Add more specific labels to query
- **Missing container logs**: Ensure container has proper labels

## Best Practices

1. **Use time ranges**: Always specify reasonable time ranges to avoid overloading
2. **Add service labels**: Filter by service first, then add text patterns
3. **Use patterns**: Extract structured data with `pattern` parser
4. **Aggregate wisely**: Use `sum`, `count_over_time` for statistics
5. **Save useful queries**: Create dashboard panels for frequently used queries

## Integration with Existing Monitoring

Loki complements your existing monitoring:
- **Prometheus**: Metrics and performance data
- **Loki**: Logs and error messages
- **TimescaleDB**: Long-term metrics storage
- **Dashboard**: Real-time business metrics

Together they provide complete observability of your Omotesamba system.