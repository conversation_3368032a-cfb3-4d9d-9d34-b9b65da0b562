# Redis Pub/Sub Settings Migration Guide

## Overview

This guide covers the migration from file-based settings polling to Redis Pub/Sub for reactive service configuration management.

## Architecture Change

### Before (File-based)
- Services poll `settings.json` every 5 seconds
- Race conditions possible with concurrent access
- 5-second delay for configuration changes
- File I/O overhead and corruption risk

### After (Redis Pub/Sub)
- Real-time configuration updates via Redis pub/sub
- Instant notification delivery (< 100ms)
- No polling overhead
- Atomic operations and consistency
- Centralized configuration management

## Components Created

### 1. Redis Pub/Sub Manager (`shared/utils/redis_pubsub_manager.py`)
- Centralized pub/sub infrastructure
- Connection recovery and error handling
- Message routing to handlers
- Health monitoring

### 2. Redis Settings Store (`shared/utils/redis_settings_store.py`)
- Redis Hash-based settings storage
- Automatic pub/sub notifications on changes
- Fallback to settings.json for disaster recovery
- Atomic operations for consistency

### 3. Reactive Service Controller (`shared/utils/reactive_service_controller.py`)
- Instant settings updates via pub/sub subscriptions
- Replaces file polling with event-driven architecture
- Multi-channel subscriptions (service-specific, emergency, global)
- Connection recovery and fallback handling

### 4. Hybrid Service Controller (`shared/utils/hybrid_service_controller.py`)
- Supports both file-based and Redis pub/sub modes
- Gradual migration capability
- Runtime mode switching
- Comprehensive health checks

### 5. Reactive Settings API (`service-dashboard/backend/api/reactive_settings_api.py`)
- Publishes changes via pub/sub after Redis updates
- Maintains backward compatibility
- Instant notification to all subscribers
- File backup for disaster recovery

## Redis Key Structure

```
settings:emergency_stop          - Hash: Emergency stop configuration
settings:processing             - Hash: Global processing settings
settings:service:{service_name} - Hash: Individual service settings
```

## Pub/Sub Channel Structure

```
settings:emergency_stop         - Emergency stop state changes
settings:service:{service_name} - Individual service state changes
settings:global                 - Global processing settings changes
```

## Migration Process

### Phase 1: Infrastructure Setup ✅
- [x] Redis Pub/Sub Manager
- [x] Redis Settings Store
- [x] Reactive Service Controller
- [x] Hybrid Service Controller
- [x] Reactive Settings API

### Phase 2: Testing & Validation
- [x] Comprehensive test suite (`scripts/test_reactive_settings.py`)
- [x] Infrastructure validation
- [x] Real-time notification testing
- [x] Hybrid controller testing

### Phase 3: Gradual Migration (Next Steps)
1. Initialize Redis settings from current file
2. Deploy reactive settings API alongside current API
3. Migrate categorizer service to hybrid controller
4. Migrate pro-analyzer service to hybrid controller
5. Migrate remaining non-critical services
6. Migrate critical services (pdf-extractor, scrapers)
7. Switch to reactive-only mode
8. Remove legacy file-based code

## Deployment Instructions

### 1. Test Infrastructure
```bash
# Run comprehensive test suite
cd /home/<USER>/dev/pair/omotesamba
python scripts/test_reactive_settings.py
```

### 2. Initialize Redis Settings
```bash
# Connect to Redis and initialize from file
docker exec -it omotesamba-redis redis-cli
```

### 3. Deploy Reactive Settings API
```bash
# Add reactive settings API to docker-compose.yml
# Update settings-api service to use reactive_settings_api.py
```

### 4. Migrate Services Individually
```python
# Example: Update categorizer service
from shared.utils.hybrid_service_controller import create_service_controller

# Replace ServiceController with HybridServiceController
controller = create_service_controller("categorizer", prefer_reactive=True)
await controller.initialize(queue_runner)
```

## Benefits Achieved

### Performance
- **Instant Updates**: Configuration changes apply immediately (vs 5-second polling)
- **Reduced Overhead**: No file I/O polling across all services
- **Better Scalability**: Redis pub/sub scales with service count

### Reliability
- **Atomic Operations**: No race conditions or partial reads
- **Connection Recovery**: Automatic reconnection and error handling
- **Fallback Support**: File-based fallback for disaster scenarios

### Observability
- **Real-time Monitoring**: Live configuration change tracking
- **Health Checks**: Comprehensive status for both Redis and file modes
- **Audit Trail**: All configuration changes logged with timestamps

## Monitoring

### Health Endpoints
- `GET /api/v1/pubsub/status` - Redis pub/sub status
- Hybrid controller health checks
- Settings store connection status

### Key Metrics
- Configuration change delivery time (target: < 100ms)
- Redis connection stability
- Pub/sub message delivery success rate
- Service mode distribution (reactive vs file-based)

## Rollback Plan

If issues occur during migration:

1. **Immediate**: Services fall back to file-based mode automatically
2. **API Level**: Switch settings API back to original file-based version
3. **Service Level**: Update services to use original ServiceController
4. **Infrastructure**: Keep Redis settings as backup/secondary

## Security Considerations

- Redis access limited to internal Docker network
- No authentication credentials in pub/sub messages
- Settings validation at store level
- Audit logging for all configuration changes

## Future Enhancements

### Phase 4: Advanced Features
- [ ] WebSocket integration for real-time dashboard updates
- [ ] Settings versioning and rollback capabilities
- [ ] Configuration approval workflows
- [ ] Multi-environment settings management

### Phase 5: Full Reactive Architecture
- [ ] Replace all file-based polling with pub/sub
- [ ] Implement distributed configuration consensus
- [ ] Add settings change impact analysis
- [ ] Performance optimization for high-frequency updates

## Testing Results

The comprehensive test suite validates:
- ✅ Redis connection and basic operations
- ✅ Settings store CRUD operations
- ✅ Pub/sub infrastructure and message delivery
- ✅ Settings migration from file to Redis
- ✅ Service update notifications
- ✅ Emergency stop notifications
- ✅ Hybrid controller mode switching
- ✅ Real-time notification performance (< 100ms delivery)

## Conclusion

This migration provides a foundation for:
- **Immediate**: Instant configuration updates and better reliability
- **Short-term**: Simplified service management and reduced overhead
- **Long-term**: Advanced reactive architecture and real-time capabilities

The hybrid approach ensures zero-downtime migration with automatic fallback, making this a low-risk, high-value architectural improvement.