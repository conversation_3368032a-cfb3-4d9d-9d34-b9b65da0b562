name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

permissions:
  contents: read
  actions: read
  checks: write
  security-events: write
  pull-requests: write

env:
  PYTHON_VERSION: "3.12"

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: timescale/timescaledb:latest-pg16
        env:
          POSTGRES_USER: omotesamba
          POSTGRES_PASSWORD: omotesamba
          POSTGRES_DB: omotesamba_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        enable-cache: true
        cache-dependency-glob: "pyproject.toml"

    - name: Create virtual environment
      run: uv venv

    - name: Install dependencies
      run: |
        source .venv/bin/activate
        uv pip install -e ".[dev]"

    - name: Run pre-commit hooks
      run: |
        source .venv/bin/activate
        pre-commit run --all-files
      continue-on-error: true  # Allow some hooks to fail for now

    - name: Run tests with coverage
      env:
        DATABASE_URL: postgresql+asyncpg://omotesamba:omotesamba@localhost:5432/omotesamba_test
        REDIS_URL: redis://localhost:6379/0
        TEST_DATABASE_URL: postgresql+asyncpg://omotesamba:omotesamba@localhost:5432/omotesamba_test
      run: |
        source .venv/bin/activate
        pytest tests/ -v --cov=services --cov=shared --cov-report=term-missing --cov-report=xml

    - name: Upload coverage reports
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  lint:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install uv
      uses: astral-sh/setup-uv@v3

    - name: Create virtual environment
      run: uv venv

    - name: Install dependencies
      run: |
        source .venv/bin/activate
        uv pip install -e ".[dev]"

    - name: Run ruff
      run: |
        source .venv/bin/activate
        ruff check .

    - name: Run black
      run: |
        source .venv/bin/activate
        black --check .

    - name: Run mypy
      run: |
        source .venv/bin/activate
        mypy services/ shared/ --ignore-missing-imports
      continue-on-error: true  # Allow mypy to fail for now

  security:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'
      continue-on-error: true

  docker:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: omotesamba:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
