# ABOUTME: Dockerfile for MinerU 2.1.0 with full multi-language support including Portuguese
# ABOUTME: Latest MinerU with GPU acceleration and comprehensive language coverage

FROM nvidia/cuda:12.1-devel-ubuntu22.04

# Avoid interactive prompts during installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Python and pip
    python3 \
    python3-pip \
    python3-dev \
    # Build tools
    build-essential \
    cmake \
    git \
    wget \
    curl \
    unzip \
    # PDF processing tools
    poppler-utils \
    # OCR and language support
    tesseract-ocr \
    tesseract-ocr-eng \
    tesseract-ocr-jpn \
    tesseract-ocr-jpn-vert \
    tesseract-ocr-kor \
    tesseract-ocr-chi-sim \
    tesseract-ocr-chi-tra \
    tesseract-ocr-por \
    tesseract-ocr-spa \
    tesseract-ocr-fra \
    tesseract-ocr-deu \
    tesseract-ocr-rus \
    tesseract-ocr-ara \
    # Fonts for multi-language support
    fonts-noto-cjk \
    fonts-noto-cjk-extra \
    fonts-noto-color-emoji \
    fonts-liberation \
    # Library dependencies
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    # Cleanup
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Upgrade pip and install Python dependencies
RUN python3 -m pip install --upgrade pip setuptools wheel

# Install PyTorch with CUDA 12.1 support
RUN pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Install MinerU 2.1.0 with all dependencies
RUN pip install --no-cache-dir \
    mineru==2.1.0 \
    magic-pdf[full]==0.6.1 \
    # Additional dependencies for language support
    paddlepaddle-gpu \
    paddleocr \
    # SGLang for VLM backend
    sglang[all] \
    # Other useful packages
    requests \
    pydantic \
    fastapi \
    uvicorn

# Set environment variables for optimal GPU usage
ENV CUDA_VISIBLE_DEVICES=0
ENV PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
ENV FORCE_CUDA=1
ENV TESSDATA_PREFIX=/usr/share/tesseract-ocr/5/tessdata

# Create directories for MinerU
RUN mkdir -p /app/data /tmp/mineru /app/models

# Download and configure PaddleOCR models for better language support
RUN python3 -c "\
import paddle; \
from paddleocr import PaddleOCR; \
print('Downloading PaddleOCR models...'); \
for lang in ['en', 'ch', 'japan', 'korean', 'latin']: \
    try: \
        ocr = PaddleOCR(use_angle_cls=True, lang=lang, use_gpu=True); \
        print(f'Downloaded {lang} models'); \
    except Exception as e: \
        print(f'Warning: Could not download {lang} models: {e}'); \
"

# Create startup script
RUN echo '#!/bin/bash' > /app/start_mineru.sh && \
    echo 'set -e' >> /app/start_mineru.sh && \
    echo '' >> /app/start_mineru.sh && \
    echo 'echo "Starting MinerU 2.1.0 with multi-language support..."' >> /app/start_mineru.sh && \
    echo 'echo "CUDA_VISIBLE_DEVICES: $CUDA_VISIBLE_DEVICES"' >> /app/start_mineru.sh && \
    echo 'echo "Available GPUs:"' >> /app/start_mineru.sh && \
    echo 'nvidia-smi -L || echo "No NVIDIA GPUs detected"' >> /app/start_mineru.sh && \
    echo '' >> /app/start_mineru.sh && \
    echo '# Check MinerU version' >> /app/start_mineru.sh && \
    echo 'python3 -c "import mineru; print(f\"MinerU version: {mineru.__version__}\")" || echo "Could not get MinerU version"' >> /app/start_mineru.sh && \
    echo '' >> /app/start_mineru.sh && \
    echo '# Test CUDA availability' >> /app/start_mineru.sh && \
    echo 'python3 -c "import torch; print(\"PyTorch version:\", torch.__version__); print(\"CUDA available:\", torch.cuda.is_available())"' >> /app/start_mineru.sh && \
    echo '' >> /app/start_mineru.sh && \
    echo '# Start SGLang server for VLM backend' >> /app/start_mineru.sh && \
    echo 'echo "Starting SGLang server..."' >> /app/start_mineru.sh && \
    echo 'export CUDA_VISIBLE_DEVICES=0' >> /app/start_mineru.sh && \
    echo '' >> /app/start_mineru.sh && \
    echo '# Try to start SGLang server in background' >> /app/start_mineru.sh && \
    echo 'nohup python3 -m sglang.launch_server --model-path microsoft/layoutlmv3-base --host 0.0.0.0 --port 30000 --mem-fraction-static 0.8 > /tmp/sglang.log 2>&1 &' >> /app/start_mineru.sh && \
    echo '' >> /app/start_mineru.sh && \
    echo '# Wait for server to start' >> /app/start_mineru.sh && \
    echo 'sleep 10' >> /app/start_mineru.sh && \
    echo '' >> /app/start_mineru.sh && \
    echo '# Keep container running and provide shell access' >> /app/start_mineru.sh && \
    echo 'echo "MinerU 2.1.0 ready!"' >> /app/start_mineru.sh && \
    echo 'echo "Available languages: en, ch, japan, korean, pt, es, fr, de, ru, ar, ta, te, ka"' >> /app/start_mineru.sh && \
    echo 'echo "SGLang server running on port 30000"' >> /app/start_mineru.sh && \
    echo '' >> /app/start_mineru.sh && \
    echo '# If arguments provided, execute them; otherwise start shell' >> /app/start_mineru.sh && \
    echo 'if [ $# -eq 0 ]; then' >> /app/start_mineru.sh && \
    echo '    exec /bin/bash' >> /app/start_mineru.sh && \
    echo 'else' >> /app/start_mineru.sh && \
    echo '    exec "$@"' >> /app/start_mineru.sh && \
    echo 'fi' >> /app/start_mineru.sh

RUN chmod +x /app/start_mineru.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python3 -c "import torch; assert torch.cuda.is_available()" || exit 1

# Expose SGLang port
EXPOSE 30000

# Set entrypoint
ENTRYPOINT ["/app/start_mineru.sh"]
