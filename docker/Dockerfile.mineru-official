# CRITICAL: MinerU 2.1.0 OFFICIAL GPU-ONLY INSTALLATION
# Following official MinerU documentation exactly
FROM python:3.10-slim

# CRITICAL: Force GPU usage - NO CPU FALLBACK ALLOWED
ENV CUDA_VISIBLE_DEVICES=0
ENV MINERU_DEVICE=cuda
ENV FORCE_CUDA=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    build-essential \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libfontconfig1 \
    libxrender1 \
    libgl1-mesa-glx \
    libgtk-3-0 \
    && rm -rf /var/lib/apt/lists/*

# Create working directory
WORKDIR /app

# CRITICAL: Install exact MinerU 2.1.0 following official documentation
# Step 1: Install PyTorch with CUDA support first
RUN pip install --no-cache-dir \
    torch torchvision torchaudio \
    --index-url https://download.pytorch.org/whl/cu121

# Step 2: Install MinerU 2.1.0 with full dependencies
RUN pip install --no-cache-dir \
    "mineru[full]==2.1.0"

# Step 3: Install sglang for VLM acceleration (as documented)
RUN pip install --no-cache-dir sglang[all]

# Step 4: Install API server dependencies
RUN pip install --no-cache-dir \
    fastapi \
    uvicorn \
    requests \
    redis \
    gradio

# Test CUDA availability and cache models
RUN python3 -c "import torch; print('CUDA Available:', torch.cuda.is_available()); print('CUDA Device Count:', torch.cuda.device_count()); print('PyTorch version:', torch.__version__)"

# Copy MinerU API server
COPY mineru_api_server.py /app/mineru_api.py
RUN chmod +x /app/mineru_api.py

# Expose API port
EXPOSE 30000

# Health check command
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:30000/health || exit 1

# CRITICAL: Start MinerU API with GPU verification
CMD ["python3", "/app/mineru_api.py"]
