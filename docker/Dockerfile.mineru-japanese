# ABOUTME: Dockerfile for MinerU 2.1.0 with full multi-language support including Portuguese
# ABOUTME: Upgraded from 2.0.6 to support Portuguese, Spanish, French, German, Russian in addition to Japanese/CJK

FROM mineru-japanese:latest

# Install multi-language support and dependencies
RUN apt-get update && apt-get install -y \
    # OpenGL libraries for cv2
    libgl1 \
    libglib2.0-0 \
    # Multi-language fonts
    fonts-noto-cjk \
    fonts-noto-cjk-extra \
    # PDF text extraction tools
    poppler-utils \
    # OCR support for multiple languages
    tesseract-ocr \
    tesseract-ocr-jpn \
    tesseract-ocr-jpn-vert \
    tesseract-ocr-por \
    tesseract-ocr-spa \
    tesseract-ocr-fra \
    tesseract-ocr-deu \
    tesseract-ocr-rus \
    # Python packages for PDF processing
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Upgrade MinerU to 2.1.0 for Portuguese and multi-language support
RUN pip install --break-system-packages --no-cache-dir --upgrade \
    mineru==2.1.0

# Install additional Python packages for multi-language support
RUN pip install --break-system-packages --no-cache-dir \
    pdfplumber \
    pymupdf \
    pytesseract

# Create Japanese extraction wrapper script
RUN mkdir -p /usr/local/bin
COPY <<'EOF' /usr/local/bin/japanese_mineru.py
#!/usr/bin/env python3
"""
MinerU Japanese Extraction Wrapper
Ensures proper Japanese text extraction
"""

import sys
import os
import subprocess
import json
import tempfile
import glob

def extract_with_japanese_support(pdf_path, output_dir):
    """Extract PDF with Japanese language support"""

    print("Extracting with Japanese support...")

    # First, check if PDF has CJK text
    check_result = subprocess.run(
        ['pdftotext', '-enc', 'UTF-8', pdf_path, '-'],
        capture_output=True, text=True, encoding='utf-8'
    )

    has_cjk = False
    if check_result.returncode == 0:
        text_sample = check_result.stdout[:1000]
        has_cjk = any(0x3000 <= ord(c) <= 0x9FFF or 0xFF00 <= ord(c) <= 0xFFEF for c in text_sample)

    if not has_cjk:
        # Use standard VLM extraction for non-CJK
        print("No CJK text detected, using standard extraction...")
        cmd = [
            'mineru', '-p', pdf_path, '-o', output_dir,
            '-b', 'vlm-sglang-client', '-u', 'http://localhost:30000'
        ]
    else:
        print("CJK text detected, using optimized extraction...")
        # Try pipeline backend first
        env = os.environ.copy()
        env['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

        cmd = [
            'mineru', '-p', pdf_path, '-o', output_dir,
            '-b', 'pipeline', '-l', 'japan', '-d', 'cuda', '--vram', '15'
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, env=env)

        if result.returncode != 0 and "CUDA out of memory" in result.stderr:
            print("Pipeline failed due to memory, falling back to VLM...")
            # Fallback to VLM
            cmd = [
                'mineru', '-p', pdf_path, '-o', output_dir,
                '-b', 'vlm-sglang-client', '-u', 'http://localhost:30000'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # Post-process to add Japanese text
                post_process_japanese(output_dir, pdf_path)

        return result.returncode == 0

    result = subprocess.run(cmd, capture_output=True, text=True)
    return result.returncode == 0

def post_process_japanese(output_dir, pdf_path):
    """Post-process to fix Japanese text using OCR/pdftotext"""

    print("Post-processing for Japanese text...")

    # Get text using pdftotext
    text_result = subprocess.run(
        ['pdftotext', '-enc', 'UTF-8', pdf_path, '-'],
        capture_output=True, text=True, encoding='utf-8'
    )

    if text_result.returncode == 0:
        # Find and update files
        md_files = glob.glob(f"{output_dir}/**/*.md", recursive=True)
        json_files = glob.glob(f"{output_dir}/**/*_content_list.json", recursive=True)

        for md_file in md_files:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()

            if '( )' in content:
                content = "⚠️ Note: Japanese text required post-processing\n\n" + content
                with open(md_file, 'w', encoding='utf-8') as f:
                    f.write(content)

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: japanese_mineru.py <input.pdf> <output_dir>")
        sys.exit(1)

    success = extract_with_japanese_support(sys.argv[1], sys.argv[2])
    sys.exit(0 if success else 1)
EOF

# Make the wrapper executable
RUN chmod +x /usr/local/bin/japanese_mineru.py

# Create an entrypoint script that handles both modes
COPY <<'EOF' /entrypoint.sh
#!/bin/bash
set -e

# If no arguments, start the sglang server (default behavior)
if [ $# -eq 0 ]; then
    echo "Starting MinerU sglang server..."
    exec mineru-sglang-server
elif [ "$1" = "mineru" ]; then
    # Direct mineru command
    exec "$@"
elif [ "$1" = "japanese" ]; then
    # Japanese extraction mode
    shift
    exec python3 /usr/local/bin/japanese_mineru.py "$@"
else
    # Pass through any other command
    exec "$@"
fi
EOF

RUN chmod +x /entrypoint.sh

# Set environment variables for better GPU memory management
ENV PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
ENV CUDA_VISIBLE_DEVICES=0

# Expose the sglang server port
EXPOSE 30000

# Set the entrypoint
ENTRYPOINT ["/entrypoint.sh"]
