{"annotations": {"title": "List issues", "readOnlyHint": true}, "description": "List issues in a GitHub repository.", "inputSchema": {"properties": {"direction": {"description": "Sort direction", "enum": ["asc", "desc"], "type": "string"}, "labels": {"description": "Filter by labels", "items": {"type": "string"}, "type": "array"}, "owner": {"description": "Repository owner", "type": "string"}, "page": {"description": "Page number for pagination (min 1)", "minimum": 1, "type": "number"}, "perPage": {"description": "Results per page for pagination (min 1, max 100)", "maximum": 100, "minimum": 1, "type": "number"}, "repo": {"description": "Repository name", "type": "string"}, "since": {"description": "Filter by date (ISO 8601 timestamp)", "type": "string"}, "sort": {"description": "Sort order", "enum": ["created", "updated", "comments"], "type": "string"}, "state": {"description": "Filter by state", "enum": ["open", "closed", "all"], "type": "string"}}, "required": ["owner", "repo"], "type": "object"}, "name": "list_issues"}