{"annotations": {"title": "Mark all notifications as read", "readOnlyHint": false}, "description": "Mark all notifications as read", "inputSchema": {"properties": {"lastReadAt": {"description": "Describes the last point that notifications were checked (optional). Default: Now", "type": "string"}, "owner": {"description": "Optional repository owner. If provided with repo, only notifications for this repository are marked as read.", "type": "string"}, "repo": {"description": "Optional repository name. If provided with owner, only notifications for this repository are marked as read.", "type": "string"}}, "type": "object"}, "name": "mark_all_notifications_read"}