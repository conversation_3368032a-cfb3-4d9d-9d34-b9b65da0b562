{"annotations": {"title": "Update pull request branch", "readOnlyHint": false}, "description": "Update the branch of a pull request with the latest changes from the base branch.", "inputSchema": {"properties": {"expectedHeadSha": {"description": "The expected SHA of the pull request's HEAD ref", "type": "string"}, "owner": {"description": "Repository owner", "type": "string"}, "pullNumber": {"description": "Pull request number", "type": "number"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo", "pullNumber"], "type": "object"}, "name": "update_pull_request_branch"}