{"annotations": {"title": "Search pull requests", "readOnlyHint": true}, "description": "Search for pull requests in GitHub repositories using issues search syntax already scoped to is:pr", "inputSchema": {"properties": {"order": {"description": "Sort order", "enum": ["asc", "desc"], "type": "string"}, "owner": {"description": "Optional repository owner. If provided with repo, only notifications for this repository are listed.", "type": "string"}, "page": {"description": "Page number for pagination (min 1)", "minimum": 1, "type": "number"}, "perPage": {"description": "Results per page for pagination (min 1, max 100)", "maximum": 100, "minimum": 1, "type": "number"}, "query": {"description": "Search query using GitHub pull request search syntax", "type": "string"}, "repo": {"description": "Optional repository name. If provided with owner, only notifications for this repository are listed.", "type": "string"}, "sort": {"description": "Sort field by number of matches of categories, defaults to best match", "enum": ["comments", "reactions", "reactions-+1", "reactions--1", "reactions-smile", "reactions-thinking_face", "reactions-heart", "reactions-tada", "interactions", "created", "updated"], "type": "string"}}, "required": ["query"], "type": "object"}, "name": "search_pull_requests"}