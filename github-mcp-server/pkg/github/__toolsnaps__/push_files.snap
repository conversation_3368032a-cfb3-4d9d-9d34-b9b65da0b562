{"annotations": {"title": "Push files to repository", "readOnlyHint": false}, "description": "Push multiple files to a GitHub repository in a single commit", "inputSchema": {"properties": {"branch": {"description": "Branch to push to", "type": "string"}, "files": {"description": "Array of file objects to push, each object with path (string) and content (string)", "items": {"additionalProperties": false, "properties": {"content": {"description": "file content", "type": "string"}, "path": {"description": "path to the file", "type": "string"}}, "required": ["path", "content"], "type": "object"}, "type": "array"}, "message": {"description": "Commit message", "type": "string"}, "owner": {"description": "Repository owner", "type": "string"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo", "branch", "files", "message"], "type": "object"}, "name": "push_files"}