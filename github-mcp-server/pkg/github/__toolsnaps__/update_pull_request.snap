{"annotations": {"title": "Edit pull request", "readOnlyHint": false}, "description": "Update an existing pull request in a GitHub repository.", "inputSchema": {"properties": {"base": {"description": "New base branch name", "type": "string"}, "body": {"description": "New description", "type": "string"}, "maintainer_can_modify": {"description": "Allow maintainer edits", "type": "boolean"}, "owner": {"description": "Repository owner", "type": "string"}, "pullNumber": {"description": "Pull request number to update", "type": "number"}, "repo": {"description": "Repository name", "type": "string"}, "state": {"description": "New state", "enum": ["open", "closed"], "type": "string"}, "title": {"description": "New title", "type": "string"}}, "required": ["owner", "repo", "pullNumber"], "type": "object"}, "name": "update_pull_request"}