{"annotations": {"title": "Get issue details", "readOnlyHint": true}, "description": "Get details of a specific issue in a GitHub repository.", "inputSchema": {"properties": {"issue_number": {"description": "The number of the issue", "type": "number"}, "owner": {"description": "The owner of the repository", "type": "string"}, "repo": {"description": "The name of the repository", "type": "string"}}, "required": ["owner", "repo", "issue_number"], "type": "object"}, "name": "get_issue"}