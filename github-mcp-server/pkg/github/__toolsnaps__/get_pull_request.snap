{"annotations": {"title": "Get pull request details", "readOnlyHint": true}, "description": "Get details of a specific pull request in a GitHub repository.", "inputSchema": {"properties": {"owner": {"description": "Repository owner", "type": "string"}, "pullNumber": {"description": "Pull request number", "type": "number"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo", "pullNumber"], "type": "object"}, "name": "get_pull_request"}