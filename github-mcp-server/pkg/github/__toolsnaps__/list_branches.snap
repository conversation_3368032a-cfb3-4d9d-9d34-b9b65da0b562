{"annotations": {"title": "List branches", "readOnlyHint": true}, "description": "List branches in a GitHub repository", "inputSchema": {"properties": {"owner": {"description": "Repository owner", "type": "string"}, "page": {"description": "Page number for pagination (min 1)", "minimum": 1, "type": "number"}, "perPage": {"description": "Results per page for pagination (min 1, max 100)", "maximum": 100, "minimum": 1, "type": "number"}, "repo": {"description": "Repository name", "type": "string"}}, "required": ["owner", "repo"], "type": "object"}, "name": "list_branches"}