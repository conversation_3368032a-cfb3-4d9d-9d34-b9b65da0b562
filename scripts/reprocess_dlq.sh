#!/bin/bash
# Reprocess documents from DLQ queues

# Default values
SOURCE_QUEUE="extraction_quality_dlq"
TARGET_QUEUE="extraction_queue"
LIMIT=10

# Process command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --source)
      SOURCE_QUEUE="$2"
      shift 2
      ;;
    --target)
      TARGET_QUEUE="$2"
      shift 2
      ;;
    --limit)
      LIMIT="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

echo "Reprocessing up to $LIMIT documents from $SOURCE_QUEUE to $TARGET_QUEUE"

# Get queue length
QUEUE_LENGTH=$(docker exec omotesamba-redis redis-cli llen "$SOURCE_QUEUE")
echo "Queue $SOURCE_QUEUE has $QUEUE_LENGTH documents"

# Determine how many documents to process
if [ "$LIMIT" -gt "$QUEUE_LENGTH" ]; then
  PROCESS_COUNT=$QUEUE_LENGTH
else
  PROCESS_COUNT=$LIMIT
fi

echo "Will process $PROCESS_COUNT documents"

# Process documents
MOVED_COUNT=0
for ((i=0; i<PROCESS_COUNT; i++)); do
  # Get document from source queue
  DOC=$(docker exec omotesamba-redis redis-cli lpop "$SOURCE_QUEUE")
  
  if [ -z "$DOC" ]; then
    echo "No more documents in queue"
    break
  fi
  
  # Push to target queue
  docker exec omotesamba-redis redis-cli lpush "$TARGET_QUEUE" "$DOC"
  
  MOVED_COUNT=$((MOVED_COUNT + 1))
  echo "Moved document $MOVED_COUNT of $PROCESS_COUNT"
done

echo "Successfully moved $MOVED_COUNT documents from $SOURCE_QUEUE to $TARGET_QUEUE"
