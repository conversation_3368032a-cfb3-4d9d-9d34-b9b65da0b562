#!/usr/bin/env python3
"""
Simple script to move documents from DLQ back to extraction queue
"""

import json
import redis
from datetime import datetime

def main():
    # Connect to Redis
    r = redis.Redis(host='redis', port=6379, decode_responses=True)
    
    # Process extraction_quality_dlq
    moved_count = 0
    while True:
        doc_json = r.lpop('extraction_quality_dlq')
        if not doc_json:
            break
            
        try:
            doc = json.loads(doc_json)
            doc_id = doc.get('id', 'unknown')
            
            # Reset retry count and clear errors
            doc['retry_count'] = 0
            if 'extraction_error' in doc:
                doc['extraction_error'] = ''
            if 'failed_at' in doc:
                del doc['failed_at']
            if 'final_status' in doc:
                del doc['final_status']
                
            # Add reprocessing metadata
            doc['dlq_reprocessing'] = {
                'original_dlq_queue': 'extraction_quality_dlq',
                'reprocess_timestamp': datetime.now().isoformat(),
                'reprocess_reason': 'DLQ cleanup - improved language detection',
                'reset_retry_count': True
            }
            
            # Push to extraction queue
            r.lpush('extraction_queue', json.dumps(doc))
            moved_count += 1
            print(f'Moved document {doc_id} from extraction_quality_dlq to extraction_queue')
            
        except Exception as e:
            print(f'Error processing document: {e}')
            continue
    
    print(f'Successfully moved {moved_count} documents from extraction_quality_dlq to extraction_queue')
    
    # Also process extraction_failed_dlq
    failed_moved_count = 0
    while True:
        doc_json = r.lpop('extraction_failed_dlq')
        if not doc_json:
            break
            
        try:
            doc = json.loads(doc_json)
            doc_id = doc.get('id', 'unknown')
            
            # Reset retry count and clear errors
            doc['retry_count'] = 0
            if 'extraction_error' in doc:
                doc['extraction_error'] = ''
            if 'failed_at' in doc:
                del doc['failed_at']
            if 'final_status' in doc:
                del doc['final_status']
                
            # Add reprocessing metadata
            doc['dlq_reprocessing'] = {
                'original_dlq_queue': 'extraction_failed_dlq',
                'reprocess_timestamp': datetime.now().isoformat(),
                'reprocess_reason': 'DLQ cleanup - improved extraction logic',
                'reset_retry_count': True
            }
            
            # Push to extraction queue
            r.lpush('extraction_queue', json.dumps(doc))
            failed_moved_count += 1
            print(f'Moved document {doc_id} from extraction_failed_dlq to extraction_queue')
            
        except Exception as e:
            print(f'Error processing document: {e}')
            continue
    
    print(f'Successfully moved {failed_moved_count} documents from extraction_failed_dlq to extraction_queue')
    print(f'Total documents moved: {moved_count + failed_moved_count}')

if __name__ == '__main__':
    main()
