// ABOUTME: Real estate services monitoring page with scraper metrics
// ABOUTME: Service health, scraping activity, and performance monitoring

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Activity, Database, Clock, TrendingUp, MapPin, Home, Zap } from 'lucide-react';

interface ScrapingMetrics {
  status: 'running' | 'idle' | 'error';
  current_area: string | null;
  progress: number;
  properties_processed: number;
  total_properties: number;
  start_time: string | null;
  estimated_completion: string | null;
  last_error: string | null;
}

interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'down';
  timestamp: string;
  checks: {
    database: string;
    scheduler: string;
  };
  service: {
    name: string;
    version: string;
    uptime: string;
  };
}

interface PropertyStats {
  total_properties: number;
  new_today: number;
  price_changes: number;
  average_rent: number;
  area_breakdown: {
    [area: string]: {
      count: number;
      avg_rent: number;
      new_listings: number;
    };
  };
}

const RealEstateServices: React.FC = () => {
  const [scrapingMetrics, setScrapingMetrics] = useState<ScrapingMetrics | null>(null);
  const [serviceHealth, setServiceHealth] = useState<ServiceHealth | null>(null);
  const [propertyStats, setPropertyStats] = useState<PropertyStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const fetchMetrics = async () => {
    try {
      const [metricsRes, healthRes] = await Promise.all([
        fetch('/api/real-estate-scraper/scrape/status'),
        fetch('/api/real-estate-scraper/health')
      ]);

      if (metricsRes.ok) {
        const metrics = await metricsRes.json();
        setScrapingMetrics(metrics);
      }

      if (healthRes.ok) {
        const health = await healthRes.json();
        setServiceHealth(health);
      }

      // Mock property stats for now - replace with real API call
      setPropertyStats({
        total_properties: 1247,
        new_today: 23,
        price_changes: 8,
        average_rent: 285000,
        area_breakdown: {
          shibuya: { count: 342, avg_rent: 245000, new_listings: 7 },
          minato: { count: 456, avg_rent: 380000, new_listings: 12 },
          aoyama: { count: 234, avg_rent: 420000, new_listings: 3 },
          omotesando: { count: 215, avg_rent: 465000, new_listings: 1 }
        }
      });

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch real estate metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const triggerScrape = async () => {
    try {
      const response = await fetch('/api/real-estate-scraper/scrape/now', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          areas: ['shibuya', 'minato', 'aoyama', 'omotesando'],
          max_properties: 50,
          include_images: true
        })
      });

      if (response.ok) {
        fetchMetrics(); // Refresh metrics
      }
    } catch (error) {
      console.error('Failed to trigger scrape:', error);
    }
  };

  useEffect(() => {
    fetchMetrics();
    const interval = setInterval(fetchMetrics, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'running': return 'bg-green-500';
      case 'degraded':
      case 'idle': return 'bg-yellow-500';
      case 'down':
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Real Estate Services</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Real Estate Services</h1>
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-500">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </span>
          <Button onClick={triggerScrape} className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Trigger Scrape
          </Button>
        </div>
      </div>

      {/* Service Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scraper Service</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${getStatusColor(serviceHealth?.status || 'down')}`}></div>
              <span className="text-lg font-semibold capitalize">
                {serviceHealth?.status || 'Unknown'}
              </span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {serviceHealth?.service.name} v{serviceHealth?.service.version}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${getStatusColor(serviceHealth?.checks.database || 'down')}`}></div>
              <span className="text-lg font-semibold capitalize">
                {serviceHealth?.checks.database || 'Unknown'}
              </span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              TimescaleDB + PostgreSQL
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduler</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${getStatusColor(serviceHealth?.checks.scheduler || 'down')}`}></div>
              <span className="text-lg font-semibold capitalize">
                {serviceHealth?.checks.scheduler || 'Unknown'}
              </span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Every 30 minutes
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Scraping Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Current Scraping Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          {scrapingMetrics ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant={scrapingMetrics.status === 'running' ? 'default' : 'secondary'}>
                    {scrapingMetrics.status}
                  </Badge>
                  {scrapingMetrics.current_area && (
                    <span className="text-sm text-gray-600">
                      Processing: {scrapingMetrics.current_area}
                    </span>
                  )}
                </div>
                {scrapingMetrics.progress > 0 && (
                  <span className="text-sm font-medium">
                    {Math.round(scrapingMetrics.progress * 100)}%
                  </span>
                )}
              </div>

              {scrapingMetrics.progress > 0 && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${scrapingMetrics.progress * 100}%` }}
                  ></div>
                </div>
              )}

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Processed</p>
                  <p className="font-semibold">{scrapingMetrics.properties_processed}</p>
                </div>
                <div>
                  <p className="text-gray-500">Total</p>
                  <p className="font-semibold">{scrapingMetrics.total_properties}</p>
                </div>
                <div>
                  <p className="text-gray-500">Started</p>
                  <p className="font-semibold">
                    {scrapingMetrics.start_time
                      ? new Date(scrapingMetrics.start_time).toLocaleTimeString()
                      : 'N/A'
                    }
                  </p>
                </div>
                <div>
                  <p className="text-gray-500">ETA</p>
                  <p className="font-semibold">
                    {scrapingMetrics.estimated_completion
                      ? new Date(scrapingMetrics.estimated_completion).toLocaleTimeString()
                      : 'N/A'
                    }
                  </p>
                </div>
              </div>

              {scrapingMetrics.last_error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <span className="text-sm text-red-700">{scrapingMetrics.last_error}</span>
                </div>
              )}
            </div>
          ) : (
            <p className="text-gray-500">Unable to load scraping metrics</p>
          )}
        </CardContent>
      </Card>

      {/* Property Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Properties</CardTitle>
            <Home className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{propertyStats?.total_properties.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +{propertyStats?.new_today} today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rent</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{propertyStats?.average_rent.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Per month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New Listings</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{propertyStats?.new_today}</div>
            <p className="text-xs text-muted-foreground">
              Since yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Price Changes</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{propertyStats?.price_changes}</div>
            <p className="text-xs text-muted-foreground">
              Last 24 hours
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Area Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Area Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {propertyStats?.area_breakdown && Object.entries(propertyStats.area_breakdown).map(([area, stats]) => (
              <div key={area} className="p-4 border rounded-lg">
                <h3 className="font-semibold capitalize mb-2">{area}</h3>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Properties:</span>
                    <span className="font-medium">{stats.count}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Avg Rent:</span>
                    <span className="font-medium">¥{stats.avg_rent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">New:</span>
                    <span className="font-medium">{stats.new_listings}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RealEstateServices;
