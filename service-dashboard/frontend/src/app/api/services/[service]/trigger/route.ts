import { NextRequest, NextResponse } from 'next/server'

const SETTINGS_API_URL = process.env.DASHBOARD_API_URL || 'http://localhost:5000'

export async function POST(request: NextRequest, { params }: { params: { service: string } }) {
  try {
    const service = params.service

    const response = await fetch(`${SETTINGS_API_URL}/api/services/${service}/trigger`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      return NextResponse.json(errorData, { status: response.status })
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Service trigger API proxy error:', error)
    return NextResponse.json(
      { error: 'Service trigger API unavailable' },
      { status: 503 }
    )
  }
}
