#!/usr/bin/env python3
"""
ABOUTME: AI Analytics API endpoints for the dashboard (synchronous version)
ABOUTME: Provides AI usage metrics, costs, and model performance data
"""

import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import List

import redis
from flask import Flask, jsonify, request
from flask_cors import CORS

# Add parent directories to path
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "shared"))

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)


@app.route("/health")
def health():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "ai-analytics"})


# Redis connection
redis_client = None

# Cost estimates per 1K tokens (approximate)
COST_ESTIMATES = {
    "gemini-2.5-flash": {"input": 0.000075, "output": 0.0003},  # $0.075/1M, $0.30/1M
    "gemini-2.5-pro": {"input": 0.00125, "output": 0.005},  # $1.25/1M, $5.00/1M
    "deepseek-v3": {"input": 0.000014, "output": 0.000028},  # $0.014/1M, $0.028/1M
    "ernie-speed": {"input": 0.0001, "output": 0.0002},  # Estimated
    "ernie-4.5": {"input": 0.0003, "output": 0.0006},  # Estimated
}


def init_redis():
    """Initialize Redis connection"""
    global redis_client
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6380")
    redis_client = redis.from_url(redis_url, decode_responses=True)
    logger.info(f"Connected to main omotesamba Redis at {redis_url}")


def get_services_from_redis() -> List[str]:
    """Get list of services from Redis rate limit keys"""
    if not redis_client:
        init_redis()

    pattern = "rate_limit:*"
    keys = redis_client.keys(pattern)
    services = set()

    for key in keys:
        # Keys are in format: rate_limit:service:model:metric:period
        parts = key.split(":")
        if len(parts) >= 3:
            services.add(parts[1])

    return sorted(list(services))


def get_models_for_service(service: str) -> List[str]:
    """Get list of models for a specific service"""
    if not redis_client:
        init_redis()

    pattern = f"rate_limit:{service}:*"
    keys = redis_client.keys(pattern)
    models = set()

    for key in keys:
        parts = key.split(":")
        if len(parts) >= 3:
            models.add(parts[2])

    return sorted(list(models))


def calculate_cost(model: str, input_tokens: int, output_tokens: int) -> float:
    """Calculate cost for given model and token usage"""
    if model not in COST_ESTIMATES:
        return 0.0

    costs = COST_ESTIMATES[model]
    input_cost = (input_tokens / 1000) * costs["input"]
    output_cost = (output_tokens / 1000) * costs["output"]

    return input_cost + output_cost


@app.route("/ai/current-usage")
def get_current_usage():
    """Get current AI usage (active in the current minute)"""
    try:
        if not redis_client:
            init_redis()

        services = get_services_from_redis()
        current_usage = []

        # Get current minute key
        now = datetime.now()
        minute_key = now.strftime("%Y-%m-%d-%H-%M")

        for service in services:
            models = get_models_for_service(service)

            for model in models:
                base_key = f"rate_limit:{service}:{model}"

                # Get current minute data
                calls = int(redis_client.hget(f"{base_key}:calls:minute", minute_key) or 0)
                input_tokens = int(
                    redis_client.hget(f"{base_key}:input_tokens:minute", minute_key) or 0
                )
                output_tokens = int(
                    redis_client.hget(f"{base_key}:output_tokens:minute", minute_key) or 0
                )

                if calls > 0:
                    cost = calculate_cost(model, input_tokens, output_tokens)

                    current_usage.append(
                        {
                            "timestamp": now.isoformat(),
                            "service_name": service,
                            "model": model,
                            "calls": calls,
                            "input_tokens": input_tokens,
                            "output_tokens": output_tokens,
                            "total_tokens": input_tokens + output_tokens,
                            "cost_usd": cost,
                        }
                    )

        return jsonify(current_usage)

    except Exception as e:
        logger.error(f"Error getting current usage: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/ai/recent-calls")
def get_recent_calls():
    """Get recent AI calls"""
    try:
        if not redis_client:
            init_redis()

        limit = int(request.args.get("limit", 20))
        service_filter = request.args.get("service", None)

        services = [service_filter] if service_filter else get_services_from_redis()
        recent_calls = []

        # Look at last 60 minutes
        now = datetime.now()
        for minutes_ago in range(60):
            time_point = now - timedelta(minutes=minutes_ago)
            minute_key = time_point.strftime("%Y-%m-%d-%H-%M")

            for service in services:
                models = get_models_for_service(service)

                for model in models:
                    base_key = f"rate_limit:{service}:{model}"

                    calls = int(redis_client.hget(f"{base_key}:calls:minute", minute_key) or 0)
                    input_tokens = int(
                        redis_client.hget(f"{base_key}:input_tokens:minute", minute_key) or 0
                    )
                    output_tokens = int(
                        redis_client.hget(f"{base_key}:output_tokens:minute", minute_key) or 0
                    )

                    if calls > 0:
                        cost = calculate_cost(model, input_tokens, output_tokens)

                        recent_calls.append(
                            {
                                "timestamp": time_point.isoformat(),
                                "service_name": service,
                                "model": model,
                                "input_tokens": input_tokens,
                                "output_tokens": output_tokens,
                                "total_tokens": input_tokens + output_tokens,
                                "cost_usd": cost,
                                "status": "success",
                            }
                        )

        # Sort by timestamp and limit
        recent_calls.sort(key=lambda x: x["timestamp"], reverse=True)
        return jsonify(recent_calls[:limit])

    except Exception as e:
        logger.error(f"Error getting recent calls: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/ai/cost-summary")
def get_cost_summary():
    """Get cost summary across all services and models"""
    try:
        if not redis_client:
            init_redis()

        services = get_services_from_redis()

        cost_summary = {
            "today_total": 0.0,
            "by_service": {},
            "by_model": {},
            "top_cost_services": [],
            "top_cost_models": [],
        }

        today = datetime.now()
        today_key = today.strftime("%Y-%m-%d")

        logger.info(f"Found services: {services}")

        for service in services:
            models = get_models_for_service(service)
            logger.info(f"Service {service} has models: {models}")
            service_cost = 0.0

            for model in models:
                base_key = f"rate_limit:{service}:{model}"

                # Get today's data from day-level keys (fallback to total tokens if no breakdown)
                input_tokens = int(
                    redis_client.hget(f"{base_key}:input_tokens:day", today_key) or 0
                )
                output_tokens = int(
                    redis_client.hget(f"{base_key}:output_tokens:day", today_key) or 0
                )
                total_tokens = int(redis_client.hget(f"{base_key}:tokens:day", today_key) or 0)

                # If we don't have breakdown but have total, estimate
                if total_tokens > 0 and input_tokens == 0 and output_tokens == 0:
                    input_tokens = int(total_tokens * 0.7)  # Assume 70% input
                    output_tokens = total_tokens - input_tokens

                if input_tokens > 0 or output_tokens > 0:
                    model_cost = calculate_cost(model, input_tokens, output_tokens)
                    service_cost += model_cost

                    # Add to model summary
                    if model not in cost_summary["by_model"]:
                        cost_summary["by_model"][model] = 0.0
                    cost_summary["by_model"][model] += model_cost

            if service_cost > 0:
                cost_summary["by_service"][service] = service_cost
                cost_summary["today_total"] += service_cost

        # Create top lists
        cost_summary["top_cost_services"] = sorted(
            cost_summary["by_service"].items(), key=lambda x: x[1], reverse=True
        )[:5]

        cost_summary["top_cost_models"] = sorted(
            cost_summary["by_model"].items(), key=lambda x: x[1], reverse=True
        )[:5]

        return jsonify(cost_summary)

    except Exception as e:
        logger.error(f"Error getting cost summary: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/ai/model-comparison")
def get_model_comparison():
    """Compare performance and costs across different models"""
    try:
        if not redis_client:
            init_redis()

        services = get_services_from_redis()
        model_stats = {}

        today = datetime.now()
        today_key = today.strftime("%Y-%m-%d")

        for service in services:
            models = get_models_for_service(service)
            for model in models:
                if model not in model_stats:
                    model_stats[model] = {
                        "total_calls": 0,
                        "total_input_tokens": 0,
                        "total_output_tokens": 0,
                        "total_cost": 0.0,
                        "services_using": set(),
                        "avg_tokens_per_call": 0.0,
                        "cost_per_call": 0.0,
                        "cost_per_1k_tokens": 0.0,
                    }

                base_key = f"rate_limit:{service}:{model}"

                # Get today's data
                calls = int(redis_client.hget(f"{base_key}:calls:day", today_key) or 0)
                input_tokens = int(
                    redis_client.hget(f"{base_key}:input_tokens:day", today_key) or 0
                )
                output_tokens = int(
                    redis_client.hget(f"{base_key}:output_tokens:day", today_key) or 0
                )
                total_tokens = int(redis_client.hget(f"{base_key}:tokens:day", today_key) or 0)

                # If we don't have breakdown but have total, estimate
                if total_tokens > 0 and input_tokens == 0 and output_tokens == 0:
                    input_tokens = int(total_tokens * 0.7)  # Assume 70% input
                    output_tokens = total_tokens - input_tokens

                if calls > 0:
                    model_stats[model]["services_using"].add(service)

                model_stats[model]["total_calls"] += calls
                model_stats[model]["total_input_tokens"] += input_tokens
                model_stats[model]["total_output_tokens"] += output_tokens

                cost = calculate_cost(model, input_tokens, output_tokens)
                model_stats[model]["total_cost"] += cost

        # Calculate derived metrics
        for model, stats in model_stats.items():
            total_tokens = stats["total_input_tokens"] + stats["total_output_tokens"]

            if stats["total_calls"] > 0:
                stats["avg_tokens_per_call"] = round(total_tokens / stats["total_calls"], 1)
                stats["cost_per_call"] = round(stats["total_cost"] / stats["total_calls"], 6)

            if total_tokens > 0:
                stats["cost_per_1k_tokens"] = round((stats["total_cost"] / total_tokens) * 1000, 6)

            stats["services_using"] = list(stats["services_using"])
            stats["total_tokens"] = total_tokens

        return jsonify(model_stats)

    except Exception as e:
        logger.error(f"Error getting model comparison: {e}")
        return jsonify({"error": str(e)}), 500


if __name__ == "__main__":
    # Initialize Redis on startup
    init_redis()

    port = int(os.getenv("PORT", 8040))
    app.run(host="0.0.0.0", port=port, debug=True)
