#!/usr/bin/env python3
"""
ABOUTME: Comprehensive queue viewer API for detailed queue inspection and management
ABOUTME: Provides full CRUD operations on queue entries, consumption control, and reprocessing
"""

import json
import logging
import os
import time
from datetime import datetime
from typing import Optional

import redis
from flask import Flask, Response, jsonify, request
from flask_cors import CORS
from prometheus_client import Counter, Gauge, Histogram, generate_latest

app = Flask(__name__)
CORS(app)

# Prometheus metrics
queue_viewer_requests_total = Counter(
    "queue_viewer_requests_total", "Total queue viewer requests", ["method", "endpoint"]
)
queue_viewer_request_duration = Histogram(
    "queue_viewer_request_duration_seconds", "Queue viewer request duration"
)
queue_viewer_queue_depths = Gauge(
    "queue_viewer_queue_depth", "Queue depths monitored by viewer", ["queue_name"]
)
queue_viewer_operations_total = Counter(
    "queue_viewer_operations_total", "Queue operations performed", ["operation", "queue_name"]
)
queue_viewer_items_processed = Counter(
    "queue_viewer_items_processed_total", "Items processed via viewer", ["queue_name", "operation"]
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Redis connection
redis_host = os.getenv("REDIS_HOST", "redis")
redis_port = int(os.getenv("REDIS_PORT", "6379"))

try:
    redis_client = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)
    redis_client.ping()
    logger.info(f"Connected to Redis at {redis_host}:{redis_port}")
except Exception as e:
    logger.error(f"Failed to connect to Redis: {e}")
    redis_client = None

# All queue names in the processing pipeline - COMPREHENSIVE LIST FROM QUEUE-MONITOR
ALL_QUEUE_NAMES = [
    # Active Processing Queues
    "document_discovery_queue",  # Entry point for new documents
    "preprocessing_queue",  # Documents waiting for categorization
    "extraction_queue",  # Documents waiting for PDF extraction
    "extraction_retry_queue",  # Extraction retries
    "validation_queue",  # Pipeline validation
    # Dead Letter Queues (DLQs) - CRITICAL
    "discovery_quality_dlq",  # Failed discovery validation
    "extraction_failed_dlq",  # Failed extractions
    "market_analysis_failed_dlq",  # Failed market analysis
    "non_financial_review_dlq",  # Non-financial documents for review/deletion
    # System Queues
    "alert_queue",  # System alerts
    # Potentially existing but unused
    "extraction_completed_queue",  # Validation gate after extraction
    "extraction_quality_dlq",  # Failed extraction validation
    "categorization_completed_queue",  # Documents after categorization
    "categorization_retry_queue",
    "categorization_failed_dlq",
    "categorization_quality_dlq",  # Failed categorization validation
    "analysis_queue",  # Single queue for all categorized documents (single-queue architecture)
    "market_analysis_queue",  # All market-related documents
    "market_intelligence_queue",  # Market intelligence and insights
    "economic_data_queue",  # Economic indicators and data
    "central_bank_queue",  # Central bank policies and speeches
    "market_analysis_recovery_queue",  # Market analysis recovery
    "analysis_quality_dlq",  # Failed analysis validation
    "storage_completed_queue",  # Fully processed documents
    "market_analysis_quality_dlq",  # Failed market analysis validation
    "storage_quality_dlq",  # Failed storage validation
    "trading_signals_queue",
    "general_queue",  # Legacy queue (should be empty)
    # Legacy queues (no longer used but may have documents)
    "corporate_filing_queue",
    "fund_investment_queue",
    "debt_credit_queue",
    "regulatory_disclosure_queue",
    "legal_compliance_queue",
]

# Queue consumption control keys
CONSUMPTION_CONTROL_PREFIX = "queue_consumption_enabled:"

# Document flow tracking keys
FLOW_TRACKING_PREFIX = "queue_flow_tracking:"
FLOW_WINDOW_MINUTES = 10


def get_service_for_queue(queue_name: str) -> Optional[str]:
    """Map queue name to the service that processes it"""
    queue_to_service = {
        "extraction_queue": "pdf_extractor",
        "preprocessing_queue": "categorizer",
        "analysis_queue": "pro_analyzer",
        "market_analysis_queue": "market_analyzer",
        "validation_queue": "pipeline_validator",
        # Add more mappings as needed
    }

    # Check for partial matches (e.g., extraction_retry_queue -> pdf_extractor)
    for queue_prefix, service in queue_to_service.items():
        if queue_name.startswith(queue_prefix.split("_")[0]):
            return service

    return None


def get_queue_consumption_status(queue_name: str) -> bool:
    """Check if queue consumption is enabled based on service status in settings.json"""
    # First check if there's a service associated with this queue
    service_name = get_service_for_queue(queue_name)

    if service_name:
        # Check service status from settings.json
        try:
            settings_path = "/app/settings.json"
            if os.path.exists(settings_path):
                with open(settings_path, "r") as f:
                    settings = json.load(f)

                services = settings.get("services", {})
                service_config = services.get(service_name, {})
                enabled = service_config.get("enabled", True)

                logger.debug(f"Queue {queue_name} -> Service {service_name} enabled: {enabled}")
                return enabled
        except Exception as e:
            logger.error(f"Error reading settings for {queue_name}: {e}")

    # Fallback to Redis-based queue consumption status
    if not redis_client:
        return True  # Default to enabled if Redis unavailable

    try:
        key = f"{CONSUMPTION_CONTROL_PREFIX}{queue_name}"
        status = redis_client.get(key)
        return status != "false"  # Default to enabled unless explicitly disabled
    except Exception as e:
        logger.error(f"Error checking consumption status for {queue_name}: {e}")
        return True


def set_queue_consumption_status(queue_name: str, enabled: bool) -> bool:
    """Enable or disable queue consumption for a specific queue"""
    if not redis_client:
        return False

    try:
        key = f"{CONSUMPTION_CONTROL_PREFIX}{queue_name}"
        redis_client.set(key, "true" if enabled else "false")
        logger.info(f"Queue consumption {'enabled' if enabled else 'disabled'} for {queue_name}")
        return True
    except Exception as e:
        logger.error(f"Error setting consumption status for {queue_name}: {e}")
        return False


def track_document_addition(queue_name: str, document_count: int = 1) -> bool:
    """Track document addition to a queue for flow rate calculation"""
    if not redis_client:
        return False

    try:
        current_time = time.time()
        flow_key = f"{FLOW_TRACKING_PREFIX}{queue_name}"

        # Add timestamp(s) to sorted set - one entry per document
        for _ in range(document_count):
            redis_client.zadd(
                flow_key, {str(current_time + (_ * 0.001)): current_time + (_ * 0.001)}
            )

        # Clean up entries older than the tracking window
        cutoff_time = current_time - (FLOW_WINDOW_MINUTES * 60)
        redis_client.zremrangebyscore(flow_key, 0, cutoff_time)

        # Set expiry on the key to prevent memory leaks
        redis_client.expire(flow_key, FLOW_WINDOW_MINUTES * 60 * 2)  # Double the window time

        return True
    except Exception as e:
        logger.error(f"Error tracking document addition to {queue_name}: {e}")
        return False


def get_queue_flow_rate(queue_name: str) -> int:
    """Get number of documents added to queue in the last window"""
    if not redis_client:
        return 0

    try:
        current_time = time.time()
        cutoff_time = current_time - (FLOW_WINDOW_MINUTES * 60)
        flow_key = f"{FLOW_TRACKING_PREFIX}{queue_name}"

        # Count documents added within the window
        count = redis_client.zcount(flow_key, cutoff_time, current_time)
        return count
    except Exception as e:
        logger.error(f"Error getting flow rate for {queue_name}: {e}")
        return 0


def get_layer_flow_rate(layer_queues: list) -> int:
    """Get total documents added to all queues in a layer in the last window"""
    total_flow = 0
    for queue_name in layer_queues:
        total_flow += get_queue_flow_rate(queue_name)
    return total_flow


def get_queue_processing_rate(queue_name: str) -> dict:
    """Get processing rate for a specific queue from its associated service"""
    processing_metrics = {
        "docs_processed_10min": 0,
        "avg_processing_time": 0,
        "success_rate": 100,
        "last_activity": None
    }
    
    try:
        # Get metrics from pdf_extractor for extraction-related queues
        if queue_name in ["extraction_queue", "extraction_retry_queue", "extraction_failed_dlq"]:
            logger.info(f"🔍 Getting processing metrics for {queue_name}")
            import requests
            response = requests.get("http://pdf-extractor:8080/api/v1/metrics", timeout=3)
            logger.info(f"📊 PDF extractor response: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                logger.info(f"📈 Got metrics data: {data}")
                processing_metrics.update({
                    "docs_processed_10min": data.get("documents_per_hour", 0) // 6,  # Convert hourly to 10min
                    "avg_processing_time": data.get("avg_processing_time", 0),
                    "success_rate": data.get("success_rate", 100),
                    "last_activity": data.get("last_activity")
                })
                logger.info(f"✅ Updated processing metrics: {processing_metrics}")
        
        # TODO: Add other service metrics (categorizer, analyzer, etc.)
        
    except Exception as e:
        logger.error(f"❌ Could not get processing metrics for {queue_name}: {e}")
    
    return processing_metrics


@app.route("/api/v1/health", methods=["GET"])
def health_check():
    """Health check endpoint"""
    return jsonify(
        {
            "status": "healthy",
            "service": "queue-viewer-api",
            "version": "2.0.0",
            "redis_connected": redis_client.ping() if redis_client else False,
            "features": [
                "viewing",
                "consumption_control",
                "deletion",
                "reprocessing",
                "batch_operations",
            ],
        }
    )


@app.route("/metrics")
def metrics():
    """Prometheus metrics endpoint"""
    return Response(generate_latest(), mimetype="text/plain")


@app.route("/api/queues", methods=["GET"])
def get_queues_simple():
    """Simple queues endpoint for frontend compatibility with flow rates"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    try:
        queues = {}
        total_depth = 0

        for queue_name in ALL_QUEUE_NAMES:
            try:
                depth = redis_client.llen(queue_name)
                consumption_enabled = get_queue_consumption_status(queue_name)
                flow_rate = get_queue_flow_rate(queue_name)
                logger.info(f"🔄 Processing queue: {queue_name}")
                processing_metrics = get_queue_processing_rate(queue_name)
                logger.info(f"📋 Got processing metrics for {queue_name}: {processing_metrics}")

                queues[queue_name] = {
                    "depth": depth,
                    "consumption_enabled": consumption_enabled,
                    "flow_rate_10min": flow_rate,
                    "processing_rate_10min": processing_metrics["docs_processed_10min"],
                    "avg_processing_time": processing_metrics["avg_processing_time"],
                    "success_rate": processing_metrics["success_rate"],
                    "last_activity": processing_metrics["last_activity"],
                    "type": "list",  # All our queues are Redis lists
                }
                total_depth += depth
            except Exception as e:
                logger.error(f"Error checking queue {queue_name}: {e}")
                queues[queue_name] = {
                    "depth": 0,
                    "consumption_enabled": True,
                    "flow_rate_10min": 0,
                    "processing_rate_10min": 0,
                    "avg_processing_time": 0,
                    "success_rate": 100,
                    "last_activity": None,
                    "error": str(e),
                }

        # Try to provide summary_metrics with fallback
        summary_metrics = {
            "total_documents": total_depth,  # Use Redis queue depth as baseline
            "total_on_queue": total_depth,
            "total_finalized": 0,  # Unknown without TimescaleDB access
            "docs_added_last_hour": 0,  # Unknown without TimescaleDB access
        }

        return jsonify(
            {
                "queues": queues,
                "total_depth": total_depth,
                "timestamp": datetime.now().isoformat(),
                "summary_metrics": summary_metrics,
            }
        )

    except Exception as e:
        logger.error(f"Error getting queue overview: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queues", methods=["GET"])
def get_all_queues():
    """Get overview of all queues with basic stats"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    try:
        queues = {}
        total_depth = 0

        for queue_name in ALL_QUEUE_NAMES:
            try:
                depth = redis_client.llen(queue_name)
                consumption_enabled = get_queue_consumption_status(queue_name)

                queues[queue_name] = {
                    "depth": depth,
                    "consumption_enabled": consumption_enabled,
                    "type": "list",  # All our queues are Redis lists
                }
                total_depth += depth
            except Exception as e:
                logger.error(f"Error checking queue {queue_name}: {e}")
                queues[queue_name] = {"depth": 0, "consumption_enabled": True, "error": str(e)}

        return jsonify(
            {"queues": queues, "total_depth": total_depth, "timestamp": datetime.now().isoformat()}
        )

    except Exception as e:
        logger.error(f"Error getting queue overview: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queue-layers", methods=["GET"])
def get_queues_by_layers():
    """Get queues organized by processing layers for better visualization"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    try:
        # Define comprehensive queue organization by layers - MERGED FROM QUEUE-MONITOR
        layers = {
            "Layer 1: Discovery": {
                "description": "Document discovery and ingestion",
                "queues": ["document_discovery_queue"],
                "color": "#3B82F6",  # Blue
                "icon": "🔍",
            },
            "Layer 1.5: Discovery Validation": {
                "description": "Quality check: URL, title, source, internal_id required",
                "queues": ["discovery_quality_dlq"],
                "color": "#6B7280",  # Gray
                "icon": "⚠️",
            },
            "Layer 2: Extraction": {
                "description": "PDF extraction with MinerU",
                "queues": [
                    "extraction_completed_queue",
                    "preprocessing_queue",
                    "extraction_failed_dlq",
                    "extraction_retry_queue",
                    "extraction_quality_dlq",
                ],
                "color": "#10B981",  # Green
                "icon": "📄",
            },
            "Layer 3: Categorization": {
                "description": "Document categorization with Flash Preprocessor",
                "queues": [
                    "categorization_completed_queue",
                    "categorization_retry_queue",
                    "categorization_failed_dlq",
                    "categorization_quality_dlq",
                ],
                "color": "#F59E0B",  # Yellow
                "icon": "🏷️",
            },
            "Layer 4: Analysis": {
                "description": "Deep analysis with Pro Analyzer",
                "queues": [
                    "market_analysis_queue",
                    "market_intelligence_queue",
                    "economic_data_queue",
                    "central_bank_queue",
                    "market_analysis_recovery_queue",
                    "analysis_quality_dlq",
                ],
                "color": "#8B5CF6",  # Purple
                "icon": "🧠",
            },
            "Layer 5: Market Intelligence": {
                "description": "Market analysis and intelligence generation",
                "queues": [
                    "market_intelligence_queue",
                    "market_intelligence_completed_queue",
                    "market_analysis_recovery_queue",
                    "market_analysis_quality_dlq",
                    "market_analysis_failed_dlq",
                ],
                "color": "#8B5CF6",  # Purple
                "icon": "📊",
            },
            "Layer 5.5: Trading Signals": {
                "description": "Trading signal generation",
                "queues": [
                    "trading_signals_queue",
                    "high_priority_signals_queue",
                ],
                "color": "#F59E0B",  # Amber
                "icon": "🎯",
            },
            "Layer 6: Storage": {
                "description": "Final storage and archival",
                "queues": [
                    "storage_queue",
                    "storage_completed_queue",
                    "storage_quality_dlq",
                ],
                "color": "#EF4444",  # Red
                "icon": "💾",
            },
            "Additional Queues": {
                "description": "Other operational queues",
                "queues": ["validation_queue", "alert_queue"],
                "color": "#6B7280",  # Gray
                "icon": "⚙️",
            },
            "Special Purpose": {
                "description": "Non-financial and review queues",
                "queues": ["non_financial_review_dlq"],
                "color": "#DC2626",  # Dark red
                "icon": "🗑️",
            },
            "Legacy Queues": {
                "description": "Deprecated queues (should be empty)",
                "queues": [
                    "general_queue",
                    "corporate_filing_queue",
                    "fund_investment_queue",
                    "debt_credit_queue",
                    "regulatory_disclosure_queue",
                    "legal_compliance_queue",
                ],
                "color": "#9CA3AF",  # Light gray
                "icon": "📦",
            },
        }

        # Get current depths and consumption status for all queues
        layer_data = {}
        for layer_name, layer_info in layers.items():
            layer_queues = []
            total_depth = 0

            for queue_name in layer_info["queues"]:
                try:
                    depth = redis_client.llen(queue_name)
                    consumption_enabled = get_queue_consumption_status(queue_name)

                    status = "normal"
                    if depth > 1000:
                        status = "critical"
                    elif depth > 500:
                        status = "warning"
                    elif depth > 100:
                        status = "busy"
                    elif depth == 0:
                        status = "empty"

                    layer_queues.append(
                        {
                            "name": queue_name,
                            "depth": depth,
                            "status": status,
                            "consumption_enabled": consumption_enabled,
                        }
                    )
                    total_depth += depth
                except Exception as e:
                    logger.error(f"Error checking queue {queue_name}: {e}")
                    layer_queues.append(
                        {
                            "name": queue_name,
                            "depth": 0,
                            "status": "error",
                            "consumption_enabled": True,
                            "error": str(e),
                        }
                    )

            # Calculate flow rate for this layer
            layer_flow_rate = get_layer_flow_rate(layer_info["queues"])

            layer_data[layer_name] = {
                "description": layer_info["description"],
                "color": layer_info["color"],
                "icon": layer_info["icon"],
                "queues": layer_queues,
                "total_depth": total_depth,
                "queue_count": len(layer_queues),
                "avg_depth": total_depth / len(layer_queues) if layer_queues else 0,
                "flow_rate_10min": layer_flow_rate,
            }

        return jsonify({"layers": layer_data, "timestamp": datetime.now().isoformat()})

    except Exception as e:
        logger.error(f"Error getting queues by layers: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queues/<queue_name>", methods=["GET"])
def get_queue_details(queue_name: str):
    """Get detailed information about a specific queue including content"""
    # Exclude special endpoints
    if queue_name in ["by-layers", "layers", "status", "history"]:
        return (
            jsonify(
                {"error": f"'{queue_name}' is not a valid queue name - it's a special endpoint"}
            ),
            400,
        )

    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    if queue_name not in ALL_QUEUE_NAMES:
        return jsonify({"error": f"Invalid queue name. Allowed queues: {ALL_QUEUE_NAMES}"}), 400

    try:
        # Get pagination parameters
        offset = int(request.args.get("offset", 0))
        limit = int(request.args.get("limit", 50))

        # Get queue basic info
        depth = redis_client.llen(queue_name)
        consumption_enabled = get_queue_consumption_status(queue_name)

        # Get queue items with pagination
        items = []
        end_index = min(offset + limit, depth)

        for i in range(offset, end_index):
            try:
                item_data = redis_client.lindex(queue_name, i)
                if item_data:
                    try:
                        # Try to parse as JSON
                        parsed_item = json.loads(item_data)
                        items.append(
                            {"index": i, "data": parsed_item, "raw": item_data, "type": "json"}
                        )
                    except json.JSONDecodeError:
                        # If not JSON, store as string
                        items.append(
                            {"index": i, "data": item_data, "raw": item_data, "type": "string"}
                        )
            except Exception as e:
                logger.error(f"Error reading item {i} from {queue_name}: {e}")
                items.append({"index": i, "error": str(e), "type": "error"})

        return jsonify(
            {
                "queue_name": queue_name,
                "depth": depth,
                "consumption_enabled": consumption_enabled,
                "items": items,
                "pagination": {
                    "offset": offset,
                    "limit": limit,
                    "total": depth,
                    "returned": len(items),
                },
                "timestamp": datetime.now().isoformat(),
            }
        )

    except Exception as e:
        logger.error(f"Error getting queue details for {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queues/<queue_name>/consumption", methods=["POST"])
def set_queue_consumption(queue_name: str):
    """Enable or disable queue consumption"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    if queue_name not in ALL_QUEUE_NAMES:
        return jsonify({"error": f"Invalid queue name. Allowed queues: {ALL_QUEUE_NAMES}"}), 400

    try:
        data = request.get_json()
        if not data or "enabled" not in data:
            return jsonify({"error": "Missing 'enabled' field in request body"}), 400

        enabled = bool(data["enabled"])
        success = set_queue_consumption_status(queue_name, enabled)

        if success:
            return jsonify(
                {
                    "queue_name": queue_name,
                    "consumption_enabled": enabled,
                    "message": f"Queue consumption {'enabled' if enabled else 'disabled'} successfully",
                }
            )
        else:
            return jsonify({"error": "Failed to update consumption status"}), 500

    except Exception as e:
        logger.error(f"Error setting consumption for {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queues/<queue_name>/items/<int:index>", methods=["DELETE"])
def delete_queue_item(queue_name: str, index: int):
    """Delete a specific item from a queue by index"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    if queue_name not in ALL_QUEUE_NAMES:
        return jsonify({"error": f"Invalid queue name. Allowed queues: {ALL_QUEUE_NAMES}"}), 400

    try:
        # Get the item first to return it in response
        item_data = redis_client.lindex(queue_name, index)
        if item_data is None:
            return jsonify({"error": "Item not found at specified index"}), 404

        # Redis doesn't have direct index deletion, so we use a workaround:
        # 1. Set the item to a unique marker
        # 2. Remove the marker
        marker = f"__DELETE_MARKER_{datetime.now().timestamp()}__"
        redis_client.lset(queue_name, index, marker)
        redis_client.lrem(queue_name, 1, marker)

        return jsonify(
            {
                "queue_name": queue_name,
                "deleted_index": index,
                "deleted_item": item_data,
                "message": "Item deleted successfully",
            }
        )

    except Exception as e:
        logger.error(f"Error deleting item {index} from {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queues/<queue_name>/items/<int:index>/reprocess", methods=["POST"])
def reprocess_queue_item(queue_name: str, index: int):
    """Move an item to a reprocessing queue or mark it for reprocessing"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    if queue_name not in ALL_QUEUE_NAMES:
        return jsonify({"error": f"Invalid queue name. Allowed queues: {ALL_QUEUE_NAMES}"}), 400

    try:
        data = request.get_json() or {}
        target_queue = data.get(
            "target_queue", "document_discovery_queue"
        )  # Default to start of pipeline

        if target_queue not in ALL_QUEUE_NAMES:
            return (
                jsonify({"error": f"Invalid target queue. Allowed queues: {ALL_QUEUE_NAMES}"}),
                400,
            )

        # Get the item
        item_data = redis_client.lindex(queue_name, index)
        if item_data is None:
            return jsonify({"error": "Item not found at specified index"}), 404

        # Parse the item to add reprocessing metadata
        try:
            item_obj = json.loads(item_data)
        except json.JSONDecodeError:
            # If not JSON, wrap it
            item_obj = {"original_data": item_data}

        # Add reprocessing metadata
        item_obj["reprocessing"] = {
            "original_queue": queue_name,
            "original_index": index,
            "reprocess_timestamp": datetime.now().isoformat(),
            "reprocess_reason": data.get("reason", "Manual reprocessing"),
        }

        # Add to target queue
        redis_client.lpush(target_queue, json.dumps(item_obj))

        # Track document addition for flow rate calculation
        track_document_addition(target_queue, 1)

        # Remove from original queue (using same method as delete)
        marker = f"__REPROCESS_MARKER_{datetime.now().timestamp()}__"
        redis_client.lset(queue_name, index, marker)
        redis_client.lrem(queue_name, 1, marker)

        return jsonify(
            {
                "queue_name": queue_name,
                "target_queue": target_queue,
                "moved_index": index,
                "item": item_obj,
                "message": f"Item moved to {target_queue} for reprocessing",
            }
        )

    except Exception as e:
        logger.error(f"Error reprocessing item {index} from {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queues/<queue_name>/batch", methods=["POST"])
def batch_queue_operations(queue_name: str):
    """Perform batch operations on queue items"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    if queue_name not in ALL_QUEUE_NAMES:
        return jsonify({"error": f"Invalid queue name. Allowed queues: {ALL_QUEUE_NAMES}"}), 400

    try:
        data = request.get_json()
        if not data or "operation" not in data:
            return jsonify({"error": "Missing 'operation' field in request body"}), 400

        operation = data["operation"]
        indices = data.get("indices", [])

        if not indices:
            return jsonify({"error": "No indices specified for batch operation"}), 400

        results = []

        if operation == "delete":
            # Sort indices in descending order to maintain correct indices during deletion
            for index in sorted(indices, reverse=True):
                try:
                    item_data = redis_client.lindex(queue_name, index)
                    if item_data:
                        marker = f"__BATCH_DELETE_MARKER_{datetime.now().timestamp()}_{index}__"
                        redis_client.lset(queue_name, index, marker)
                        redis_client.lrem(queue_name, 1, marker)
                        results.append({"index": index, "status": "deleted"})
                    else:
                        results.append({"index": index, "status": "not_found"})
                except Exception as e:
                    results.append({"index": index, "status": "error", "error": str(e)})

        elif operation == "reprocess":
            target_queue = data.get("target_queue", "document_discovery_queue")
            if target_queue not in ALL_QUEUE_NAMES:
                return (
                    jsonify({"error": f"Invalid target queue. Allowed queues: {ALL_QUEUE_NAMES}"}),
                    400,
                )

            reason = data.get("reason", "Batch reprocessing")

            # Sort indices in descending order
            for index in sorted(indices, reverse=True):
                try:
                    item_data = redis_client.lindex(queue_name, index)
                    if item_data:
                        try:
                            item_obj = json.loads(item_data)
                        except json.JSONDecodeError:
                            item_obj = {"original_data": item_data}

                        # Enhanced DLQ reprocessing logic
                        is_dlq_reprocess = queue_name.endswith("_dlq") or "failed" in queue_name

                        if is_dlq_reprocess:
                            # Clean up retry metadata for fresh reprocessing
                            retry_fields_to_remove = [
                                "categorization_retry_count",
                                "extraction_retry_count",
                                "analysis_retry_count",
                                "retry_after",
                                "last_error",
                                "categorization_failed_at",
                                "extraction_failed_at",
                                "analysis_failed_at",
                                "final_error",
                                "failure_reason",
                                "retry_attempts",
                            ]

                            for field in retry_fields_to_remove:
                                item_obj.pop(field, None)

                            # Add DLQ reprocessing metadata
                            item_obj["dlq_reprocessing"] = {
                                "original_dlq_queue": queue_name,
                                "original_index": index,
                                "reprocess_timestamp": datetime.now().isoformat(),
                                "reprocess_reason": reason,
                                "reset_retry_count": True,
                                "previous_failure": item_obj.get("final_error", "Unknown error"),
                            }

                            # Track reprocessing history
                            if "reprocess_history" not in item_obj:
                                item_obj["reprocess_history"] = []

                            # Quality control: Check reprocessing count
                            reprocess_count = len(item_obj["reprocess_history"])
                            max_reprocesses = (
                                3  # Maximum times a document can be reprocessed from DLQ
                            )

                            if reprocess_count >= max_reprocesses:
                                logger.warning(
                                    f"Document {item_obj.get('id', 'unknown')} has been reprocessed {reprocess_count} times. "
                                    f"Consider quarantining or manual review."
                                )
                                # Add warning flag but allow reprocessing
                                item_obj["reprocessing_warning"] = {
                                    "message": f"Document reprocessed {reprocess_count + 1} times",
                                    "requires_review": True,
                                    "timestamp": datetime.now().isoformat(),
                                }

                            item_obj["reprocess_history"].append(
                                {
                                    "from_queue": queue_name,
                                    "to_queue": target_queue,
                                    "timestamp": datetime.now().isoformat(),
                                    "reason": reason,
                                    "reprocess_count": reprocess_count + 1,
                                }
                            )

                            logger.info(
                                f"Cleaning up DLQ document {item_obj.get('id', 'unknown')} for reprocessing from {queue_name} to {target_queue}"
                            )
                        else:
                            # Standard reprocessing metadata for non-DLQ
                            item_obj["reprocessing"] = {
                                "original_queue": queue_name,
                                "original_index": index,
                                "reprocess_timestamp": datetime.now().isoformat(),
                                "reprocess_reason": reason,
                            }

                        redis_client.lpush(target_queue, json.dumps(item_obj))

                        # Track document addition for flow rate calculation
                        track_document_addition(target_queue, 1)

                        marker = f"__BATCH_REPROCESS_MARKER_{datetime.now().timestamp()}_{index}__"
                        redis_client.lset(queue_name, index, marker)
                        redis_client.lrem(queue_name, 1, marker)

                        results.append({"index": index, "status": "reprocessed"})
                        logger.info(
                            f"✅ Successfully reprocessed document {item_obj.get('id', 'unknown')} "
                            f"from {queue_name}[{index}] to {target_queue}"
                        )
                    else:
                        results.append({"index": index, "status": "not_found"})
                except Exception as e:
                    results.append({"index": index, "status": "error", "error": str(e)})
        else:
            return jsonify({"error": f"Unknown operation: {operation}"}), 400

        # Log summary
        successful_count = len([r for r in results if r["status"] in ["deleted", "reprocessed"]])
        failed_count = len([r for r in results if r["status"] == "error"])
        not_found_count = len([r for r in results if r["status"] == "not_found"])
        
        logger.info(
            f"📊 Batch {operation} summary for {queue_name}: "
            f"Total: {len(results)}, Successful: {successful_count}, "
            f"Failed: {failed_count}, Not Found: {not_found_count}"
        )
        
        if operation == "reprocess" and successful_count > 0:
            logger.info(
                f"🚀 Moved {successful_count} documents from {queue_name} to {target_queue}"
            )
        
        return jsonify(
            {
                "queue_name": queue_name,
                "operation": operation,
                "results": results,
                "total_processed": len(results),
                "successful": successful_count,
                "failed": failed_count,
                "not_found": not_found_count,
            }
        )

    except Exception as e:
        logger.error(f"Error performing batch operation on {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queues/<queue_name>/error-analysis", methods=["GET"])
def analyze_queue_errors(queue_name: str):
    """Analyze error patterns in DLQ to help with troubleshooting"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    if queue_name not in ALL_QUEUE_NAMES:
        return jsonify({"error": f"Invalid queue name. Allowed queues: {ALL_QUEUE_NAMES}"}), 400

    # Only analyze DLQ queues
    if not (queue_name.endswith("_dlq") or "failed" in queue_name):
        return jsonify({"error": "Error analysis only available for DLQ queues"}), 400

    try:
        depth = redis_client.llen(queue_name)
        if depth == 0:
            return jsonify(
                {
                    "queue_name": queue_name,
                    "total_documents": 0,
                    "error_patterns": {},
                    "recommendations": [],
                }
            )

        # Sample up to 100 documents for analysis
        sample_size = min(depth, 100)
        error_patterns = {}
        failure_reasons = {}
        reprocess_counts = {}

        for i in range(sample_size):
            try:
                item_data = redis_client.lindex(queue_name, i)
                if item_data:
                    item_obj = json.loads(item_data)

                    # Analyze error patterns
                    final_error = item_obj.get("final_error", "Unknown error")
                    if final_error in error_patterns:
                        error_patterns[final_error] += 1
                    else:
                        error_patterns[final_error] = 1

                    # Analyze failure reasons
                    failure_reason = item_obj.get("failure_reason", "Unknown reason")
                    if failure_reason in failure_reasons:
                        failure_reasons[failure_reason] += 1
                    else:
                        failure_reasons[failure_reason] = 1

                    # Count reprocessing attempts
                    reprocess_history = item_obj.get("reprocess_history", [])
                    reprocess_count = len(reprocess_history)
                    reprocess_counts[reprocess_count] = reprocess_counts.get(reprocess_count, 0) + 1

            except (json.JSONDecodeError, Exception) as e:
                logger.warning(f"Error analyzing document at index {i}: {e}")
                continue

        # Generate recommendations
        recommendations = []

        # Check for timeout patterns
        timeout_errors = sum(
            count
            for error, count in error_patterns.items()
            if "timeout" in error.lower() or "connection" in error.lower()
        )
        if timeout_errors > sample_size * 0.3:
            recommendations.append(
                {
                    "type": "timeout",
                    "message": f"{timeout_errors} documents failed due to timeouts. Consider increasing timeout settings.",
                    "priority": "high",
                }
            )

        # Check for quota/rate limit patterns
        quota_errors = sum(
            count
            for error, count in error_patterns.items()
            if "quota" in error.lower() or "rate limit" in error.lower()
        )
        if quota_errors > 0:
            recommendations.append(
                {
                    "type": "rate_limit",
                    "message": f"{quota_errors} documents failed due to rate limits. Consider reducing batch sizes.",
                    "priority": "medium",
                }
            )

        # Check for authentication issues
        auth_errors = sum(
            count
            for error, count in error_patterns.items()
            if "auth" in error.lower() or "401" in error or "403" in error
        )
        if auth_errors > 0:
            recommendations.append(
                {
                    "type": "authentication",
                    "message": f"{auth_errors} documents failed due to authentication. Check API credentials.",
                    "priority": "high",
                }
            )

        return jsonify(
            {
                "queue_name": queue_name,
                "total_documents": depth,
                "sample_size": sample_size,
                "error_patterns": dict(
                    sorted(error_patterns.items(), key=lambda x: x[1], reverse=True)[:10]
                ),
                "failure_reasons": dict(
                    sorted(failure_reasons.items(), key=lambda x: x[1], reverse=True)[:10]
                ),
                "reprocess_counts": reprocess_counts,
                "recommendations": recommendations,
                "analysis_timestamp": datetime.now().isoformat(),
            }
        )

    except Exception as e:
        logger.error(f"Error analyzing queue {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queues/<queue_name>/search", methods=["GET"])
def search_queue(queue_name: str):
    """Search for documents in a queue by internal_id or other fields"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    if queue_name not in ALL_QUEUE_NAMES:
        return jsonify({"error": f"Invalid queue name. Allowed queues: {ALL_QUEUE_NAMES}"}), 400

    try:
        search_term = request.args.get("q", "").strip()
        if not search_term:
            return jsonify({"error": "Search term required"}), 400

        # Get all items in the queue
        depth = redis_client.llen(queue_name)
        found_items = []

        # Check if search term is a number (potential internal_id)
        search_internal_id = None
        if search_term.isdigit():
            search_internal_id = int(search_term)

        # Search through all items
        for i in range(depth):
            try:
                item_data = redis_client.lindex(queue_name, i)
                if item_data:
                    try:
                        # Parse JSON document
                        doc = json.loads(item_data)

                        # Search by internal_id if it's a number
                        if search_internal_id and doc.get("internal_id") == search_internal_id:
                            found_items.append(
                                {
                                    "index": i,
                                    "data": doc,
                                    "match_field": "internal_id",
                                    "type": "json",
                                }
                            )
                            continue

                        # Search in other fields
                        searchable_fields = [
                            "id",
                            "document_id",
                            "title",
                            "company_code",
                            "company_name",
                            "url",
                        ]
                        for field in searchable_fields:
                            value = doc.get(field)
                            if value and search_term.lower() in str(value).lower():
                                found_items.append(
                                    {"index": i, "data": doc, "match_field": field, "type": "json"}
                                )
                                break

                    except json.JSONDecodeError:
                        # Search in raw string
                        if search_term.lower() in item_data.lower():
                            found_items.append(
                                {
                                    "index": i,
                                    "data": item_data,
                                    "match_field": "raw_content",
                                    "type": "string",
                                }
                            )

            except Exception as e:
                logger.error(f"Error searching item {i} in {queue_name}: {e}")

        return jsonify(
            {
                "queue_name": queue_name,
                "search_term": search_term,
                "total_items": depth,
                "matches_found": len(found_items),
                "items": found_items,
                "timestamp": datetime.now().isoformat(),
            }
        )

    except Exception as e:
        logger.error(f"Error searching queue {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queues/<queue_name>/clear", methods=["POST"])
def clear_queue(queue_name: str):
    """Clear all items from a queue (with confirmation)"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    if queue_name not in ALL_QUEUE_NAMES:
        return jsonify({"error": f"Invalid queue name. Allowed queues: {ALL_QUEUE_NAMES}"}), 400

    try:
        data = request.get_json() or {}
        confirm = data.get("confirm", False)

        if not confirm:
            return (
                jsonify(
                    {
                        "error": "Queue clearing requires explicit confirmation",
                        "message": 'Send request with {"confirm": true} to proceed',
                    }
                ),
                400,
            )

        # Get current depth for response
        depth = redis_client.llen(queue_name)

        # Clear the queue
        redis_client.delete(queue_name)

        logger.warning(f"Queue {queue_name} cleared - {depth} items deleted")

        return jsonify(
            {
                "queue_name": queue_name,
                "items_deleted": depth,
                "message": f"Queue cleared successfully - {depth} items deleted",
            }
        )

    except Exception as e:
        logger.error(f"Error clearing queue {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


# Manual validation endpoints for pipeline bypass detection
@app.route("/api/queue-viewer/<queue_name>", methods=["GET"])
def get_queue_for_validation(queue_name: str):
    """Get documents from a specific queue with bypass detection for manual validation"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    try:
        limit = int(request.args.get("limit", 20))
        offset = int(request.args.get("offset", 0))

        # Get queue length
        queue_length = redis_client.llen(queue_name)

        # Get documents from queue (without removing them)
        documents = []
        for i in range(offset, min(offset + limit, queue_length)):
            doc_json = redis_client.lindex(queue_name, i)
            if doc_json:
                try:
                    doc = json.loads(doc_json)

                    # Analyze document quality
                    content = doc.get("extracted_text", doc.get("content", ""))
                    has_valid_content = len(content.strip()) > 100

                    # Check if document has been properly extracted
                    has_extraction_metadata = bool(
                        doc.get("extraction_metadata")
                        or doc.get("extraction_method")
                        or doc.get("extraction_timestamp")
                    )

                    # Detect potential bypass indicators
                    bypass_indicators = []
                    if not has_extraction_metadata and has_valid_content:
                        bypass_indicators.append("missing_extraction_metadata")
                    if doc.get("reprocessing"):
                        bypass_indicators.append("reprocessing_flag")
                    if "test_" in str(doc.get("id", "")):
                        bypass_indicators.append("test_document")
                    if not doc.get("extracted_text") and doc.get("content"):
                        bypass_indicators.append("content_without_extraction")

                    doc_info = {
                        "id": doc.get("id"),
                        "title": doc.get("title", "")[:100],
                        "content": content[:500] + "..." if len(content) > 500 else content,
                        "extracted_text": doc.get("extracted_text", "")[:500] + "..."
                        if len(doc.get("extracted_text", "")) > 500
                        else doc.get("extracted_text", ""),
                        "content_length": len(content),
                        "url": doc.get("url", ""),
                        "source": doc.get("source", ""),
                        "company_name": doc.get("company_name", ""),
                        "published_at": doc.get("published_at", ""),
                        "categorization": doc.get("categorization", {}),
                        "metadata": doc.get("metadata", {}),
                        "has_valid_content": has_valid_content,
                        "has_extraction_metadata": has_extraction_metadata,
                        "extraction_method": doc.get("extraction_method", ""),
                        "extraction_timestamp": doc.get("extraction_timestamp", ""),
                        "bypass_indicators": bypass_indicators,
                        "queue_position": i,
                    }
                    documents.append(doc_info)
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON in queue {queue_name} at position {i}: {e}")
                    continue

        return jsonify(
            {
                "queue_name": queue_name,
                "total_documents": queue_length,
                "returned_documents": len(documents),
                "offset": offset,
                "limit": limit,
                "documents": documents,
            }
        )

    except Exception as e:
        logger.error(f"Error getting documents from queue {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/queue-viewer/<queue_name>/approve/<doc_id>", methods=["POST"])
def approve_document(queue_name: str, doc_id: str):
    """Approve a document and move it to the next processing stage"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    try:
        # Find and remove the document from the queue
        queue_length = redis_client.llen(queue_name)
        document_found = False
        doc_data = None

        # Search for document by ID
        for i in range(queue_length):
            doc_json = redis_client.lindex(queue_name, i)
            if doc_json:
                doc = json.loads(doc_json)
                if doc.get("id") == doc_id:
                    # Remove document from current position
                    temp_marker = f"TEMP_MARKER_{datetime.now().timestamp()}"
                    redis_client.lset(queue_name, i, temp_marker)
                    redis_client.lrem(queue_name, 1, temp_marker)

                    document_found = True
                    doc_data = doc
                    break

        if not document_found:
            return jsonify({"error": f"Document {doc_id} not found in queue {queue_name}"}), 404

        # Add approval metadata
        doc_data["manual_approval"] = {
            "approved_at": datetime.now().isoformat(),
            "approved_from_queue": queue_name,
            "approved_by": "manual_validation",
        }

        # Determine next queue based on current queue
        next_queue_map = {
            "extraction_completed_queue": "corporate_filing_queue",  # Route to categorization output
            "corporate_filing_queue": "storage_completed_queue",  # Route to final storage
            "fund_investment_queue": "storage_completed_queue",
            "economic_data_queue": "storage_completed_queue",
            "debt_credit_queue": "storage_completed_queue",
            "central_bank_queue": "storage_completed_queue",
            "regulatory_disclosure_queue": "storage_completed_queue",
            "legal_compliance_queue": "storage_completed_queue",
            "general_queue": "storage_completed_queue",
        }

        next_queue = next_queue_map.get(queue_name, "storage_completed_queue")

        # Add to next queue
        redis_client.lpush(next_queue, json.dumps(doc_data))

        # Track document addition for flow rate calculation
        track_document_addition(next_queue, 1)

        logger.info(f"Document {doc_id} approved and moved from {queue_name} to {next_queue}")

        return jsonify(
            {
                "status": "success",
                "document_id": doc_id,
                "moved_from": queue_name,
                "moved_to": next_queue,
                "message": f"Document approved and moved to {next_queue}",
            }
        )

    except Exception as e:
        logger.error(f"Error approving document {doc_id} from queue {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/queue-viewer/<queue_name>/reject/<doc_id>", methods=["POST"])
def reject_document(queue_name: str, doc_id: str):
    """Reject a document and move it to dead letter queue"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    try:
        data = request.get_json() or {}
        reason = data.get("reason", "Manual rejection")

        # Find and remove the document from the queue
        queue_length = redis_client.llen(queue_name)
        document_found = False
        doc_data = None

        # Search for document by ID
        for i in range(queue_length):
            doc_json = redis_client.lindex(queue_name, i)
            if doc_json:
                doc = json.loads(doc_json)
                if doc.get("id") == doc_id:
                    # Remove document from current position
                    temp_marker = f"TEMP_MARKER_{datetime.now().timestamp()}"
                    redis_client.lset(queue_name, i, temp_marker)
                    redis_client.lrem(queue_name, 1, temp_marker)

                    document_found = True
                    doc_data = doc
                    break

        if not document_found:
            return jsonify({"error": f"Document {doc_id} not found in queue {queue_name}"}), 404

        # Add rejection metadata
        doc_data["manual_rejection"] = {
            "rejected_at": datetime.now().isoformat(),
            "rejected_from_queue": queue_name,
            "rejection_reason": reason,
            "rejected_by": "manual_validation",
        }

        # Move to dead letter queue
        dlq_name = f"{queue_name}_manual_rejected_dlq"
        redis_client.lpush(dlq_name, json.dumps(doc_data))

        # Track document addition for flow rate calculation
        track_document_addition(dlq_name, 1)

        logger.info(f"Document {doc_id} rejected from {queue_name} with reason: {reason}")

        return jsonify(
            {
                "status": "success",
                "document_id": doc_id,
                "rejected_from": queue_name,
                "moved_to": dlq_name,
                "reason": reason,
                "message": f"Document rejected and moved to {dlq_name}",
            }
        )

    except Exception as e:
        logger.error(f"Error rejecting document {doc_id} from queue {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/queue-viewer/<queue_name>/requeue-for-extraction/<doc_id>", methods=["POST"])
def requeue_for_extraction(queue_name: str, doc_id: str):
    """Move a document back to discovery queue for proper extraction"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    try:
        # Find and remove the document from the queue
        queue_length = redis_client.llen(queue_name)
        document_found = False
        doc_data = None

        # Search for document by ID
        for i in range(queue_length):
            doc_json = redis_client.lindex(queue_name, i)
            if doc_json:
                doc = json.loads(doc_json)
                if doc.get("id") == doc_id:
                    # Remove document from current position
                    temp_marker = f"TEMP_MARKER_{datetime.now().timestamp()}"
                    redis_client.lset(queue_name, i, temp_marker)
                    redis_client.lrem(queue_name, 1, temp_marker)

                    document_found = True
                    doc_data = doc
                    break

        if not document_found:
            return jsonify({"error": f"Document {doc_id} not found in queue {queue_name}"}), 404

        # Clean extraction fields to force re-extraction
        doc_data.pop("extracted_text", None)
        doc_data.pop("extracted_markdown", None)
        doc_data.pop("extraction_method", None)
        doc_data.pop("extraction_timestamp", None)
        doc_data.pop("extraction_metadata", None)
        doc_data.pop("extraction_assets", None)
        doc_data.pop("extraction_images", None)
        doc_data.pop("extraction_tables", None)
        doc_data.pop("content", None)  # Remove any fake content

        # Add reprocessing metadata
        doc_data["manual_requeue"] = {
            "requeued_at": datetime.now().isoformat(),
            "requeued_from": queue_name,
            "reason": "force_proper_extraction",
            "requeued_by": "manual_validation",
        }

        # Move to document discovery queue for proper extraction
        redis_client.lpush("document_discovery_queue", json.dumps(doc_data))

        # Track document addition for flow rate calculation
        track_document_addition("document_discovery_queue", 1)

        logger.info(f"Document {doc_id} requeued for proper extraction from {queue_name}")

        return jsonify(
            {
                "status": "success",
                "document_id": doc_id,
                "moved_from": queue_name,
                "moved_to": "document_discovery_queue",
                "message": "Document requeued for proper MinerU extraction",
            }
        )

    except Exception as e:
        logger.error(f"Error requeuing document {doc_id} from queue {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


# Additional endpoints merged from queue-monitor
@app.route("/api/v1/queues/status", methods=["GET"])
def get_queue_status():
    """Get real-time status of all queues from Redis (merged from queue-monitor)"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    try:
        queue_status = {}
        total_depth = 0

        # Get actual queue depths from Redis
        for queue_name in ALL_QUEUE_NAMES:
            try:
                depth = redis_client.llen(queue_name)
                status = "normal"

                # Determine status based on depth
                if depth > 1000:
                    status = "critical"
                elif depth > 500:
                    status = "warning"

                queue_status[queue_name] = {"depth": depth, "status": status}
                total_depth += depth
            except Exception as e:
                logger.error(f"Error checking queue {queue_name}: {e}")
                queue_status[queue_name] = {"depth": 0, "status": "error", "error": str(e)}

        # Calculate processing stats (approximate)
        stats = {
            "processed_last_hour": redis_client.get("stats:processed_last_hour") or 0,
            "average_processing_time": float(redis_client.get("stats:avg_processing_time") or 0),
            "success_rate": float(redis_client.get("stats:success_rate") or 0.95),
        }

        return jsonify(
            {
                "queues": queue_status,
                "total_depth": total_depth,
                "status": "critical"
                if total_depth > 5000
                else "warning"
                if total_depth > 1000
                else "operational",
                "timestamp": datetime.now().isoformat(),
                "alerts": [],
                "stats": stats,
            }
        )

    except Exception as e:
        logger.error(f"Error getting queue status: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/queues/history", methods=["GET"])
def get_queue_history():
    """Get historical queue depth data (merged from queue-monitor)"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    try:
        from datetime import timedelta

        hours = int(request.args.get("hours", 24))
        history = []

        # For now, return current data as we don't have historical tracking
        # In production, this would query time-series data
        current_depths = {}
        for queue_name in ALL_QUEUE_NAMES:
            current_depths[queue_name] = redis_client.llen(queue_name)

        # Generate mock historical data based on current depths
        now = datetime.now()
        for i in range(hours):
            timestamp = now - timedelta(hours=i)
            data_point = {"timestamp": timestamp.isoformat(), "queues": {}}

            # Add some variation to make it look realistic
            for queue_name, current_depth in current_depths.items():
                variation = 1 + (0.2 * (i % 5 - 2.5) / 2.5)  # ±20% variation
                data_point["queues"][queue_name] = int(current_depth * variation)

            history.append(data_point)

        return jsonify({"history": history, "hours": hours})

    except Exception as e:
        logger.error(f"Error getting queue history: {e}")
        return jsonify({"error": str(e)}), 500


# Legacy compatibility endpoints
@app.route("/api/v1/queue/documents", methods=["GET"])
def get_queue_documents():
    """Legacy endpoint - redirects to new API"""
    queue_name = request.args.get("queue", "extraction_completed_queue")
    offset = request.args.get("offset", "0")
    limit = request.args.get("limit", "20")

    # Redirect to new endpoint
    from flask import redirect, url_for

    return redirect(url_for("get_queue_details", queue_name=queue_name, offset=offset, limit=limit))


@app.route("/api/v1/queues/<queue_name>/track-additions", methods=["POST"])
def track_manual_additions(queue_name: str):
    """Manually track document additions for testing flow rate functionality"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    if queue_name not in ALL_QUEUE_NAMES:
        return jsonify({"error": f"Invalid queue name. Allowed queues: {ALL_QUEUE_NAMES}"}), 400

    try:
        data = request.get_json() or {}
        count = int(data.get("count", 1))

        if count < 1 or count > 100:
            return jsonify({"error": "Count must be between 1 and 100"}), 400

        success = track_document_addition(queue_name, count)

        if success:
            current_flow_rate = get_queue_flow_rate(queue_name)
            return jsonify(
                {
                    "queue_name": queue_name,
                    "documents_tracked": count,
                    "current_flow_rate_10min": current_flow_rate,
                    "message": f"Successfully tracked {count} document additions to {queue_name}",
                    "timestamp": datetime.now().isoformat(),
                }
            )
        else:
            return jsonify({"error": "Failed to track document additions"}), 500

    except Exception as e:
        logger.error(f"Error tracking manual additions for {queue_name}: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/api/v1/flow-rates", methods=["GET"])
def get_all_flow_rates():
    """Get flow rates for all queues and layers (for testing and debugging)"""
    if not redis_client:
        return jsonify({"error": "Redis connection not available"}), 503

    try:
        flow_rates = {}

        # Get flow rates for all individual queues
        for queue_name in ALL_QUEUE_NAMES:
            flow_rate = get_queue_flow_rate(queue_name)
            if flow_rate > 0:  # Only include queues with activity
                flow_rates[queue_name] = flow_rate

        # Calculate layer flow rates
        layers = {
            "Layer 1: Discovery": ["document_discovery_queue"],
            "Layer 1.5: Discovery Validation": ["discovery_quality_dlq"],
            "Layer 2: Extraction": [
                "extraction_completed_queue",
                "preprocessing_queue",
                "extraction_failed_dlq",
                "extraction_retry_queue",
                "extraction_quality_dlq",
            ],
            "Layer 3: Categorization": [
                "categorization_completed_queue",
                "categorization_retry_queue",
                "categorization_failed_dlq",
                "categorization_quality_dlq",
            ],
            "Layer 4: Analysis": [
                "market_analysis_queue",
                "market_intelligence_queue",
                "economic_data_queue",
                "central_bank_queue",
                "market_analysis_recovery_queue",
                "analysis_quality_dlq",
            ],
            "Layer 5: Output": [
                "storage_completed_queue",
                "market_analysis_quality_dlq",
                "storage_quality_dlq",
            ],
        }

        layer_flow_rates = {}
        for layer_name, queue_list in layers.items():
            layer_flow_rate = get_layer_flow_rate(queue_list)
            if layer_flow_rate > 0:
                layer_flow_rates[layer_name] = layer_flow_rate

        return jsonify(
            {
                "queue_flow_rates": flow_rates,
                "layer_flow_rates": layer_flow_rates,
                "window_minutes": FLOW_WINDOW_MINUTES,
                "total_active_queues": len(flow_rates),
                "timestamp": datetime.now().isoformat(),
            }
        )

    except Exception as e:
        logger.error(f"Error getting flow rates: {e}")
        return jsonify({"error": str(e)}), 500


if __name__ == "__main__":
    port = int(os.getenv("QUEUE_VIEWER_API_PORT", "8014"))
    logger.info(f"Starting Comprehensive Queue Viewer API on port {port}")
    app.run(host="0.0.0.0", port=port, debug=False)
