#!/usr/bin/env python3
"""
ABOUTME: Unified PDF extraction service with MinerU integration
ABOUTME: Processes documents from discovery queue with emergency stop support
"""

import asyncio
import json
import logging
import os
import subprocess
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Optional, Tuple
from urllib.parse import urlparse, unquote

import httpx
import redis.asyncio as redis

# Import markitdown for non-PDF file processing
try:
    from markitdown import MarkItDown
    MARKITDOWN_AVAILABLE = True
except ImportError:
    logger.warning("MarkItDown not available, non-PDF extraction will fail")
    MarkItDown = None
    MARKITDOWN_AVAILABLE = False

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import emergency stop checker and service controller
try:
    from shared.utils.emergency_stop import check_emergency_stop
except ImportError:
    logger.warning("Emergency stop not available, continuing without it")

    async def check_emergency_stop(service_name: str = None):
        return False


# Simple pause/resume functionality without external dependencies
import json
from pathlib import Path


class SimpleServiceController:
    """Lightweight service controller for pause/resume functionality"""

    def __init__(self, service_name: str, settings_path: str = "/app/settings.json"):
        self.service_name = service_name
        self.settings_path = Path(settings_path)
        self.is_enabled = True

    async def check_should_process(self) -> bool:
        """Check if service should process queues based on settings"""
        try:
            if not self.settings_path.exists():
                return True  # Default to enabled

            with open(self.settings_path, "r") as f:
                settings = json.load(f)

            # Check emergency stop
            emergency_stop = settings.get("emergency_stop", {})
            if emergency_stop.get("enabled", False):
                return False

            # Check service-specific setting
            services = settings.get("services", {})
            service_config = services.get(self.service_name, {})
            enabled = service_config.get(
                "auto_queue_processing", service_config.get("enabled", True)
            )

            if enabled != self.is_enabled:
                self.is_enabled = enabled
                status = "ENABLED" if enabled else "PAUSED"
                logger.info(f"🔄 PDF EXTRACTOR QUEUE PROCESSING {status} via dashboard")
                print(f"🔄 PDF EXTRACTOR QUEUE PROCESSING {status} via dashboard")

            return enabled

        except Exception as e:
            logger.warning(f"Error checking service settings: {e}")
            return True  # Default to enabled on error


SERVICE_CONTROLLER_AVAILABLE = True


# Import asset storage utilities
try:
    from shared.utils.asset_storage import create_lightweight_document_metadata, get_asset_storage

    ASSET_STORAGE_AVAILABLE = True
except ImportError:
    logger.warning("Asset storage utilities not available")
    get_asset_storage = None
    create_lightweight_document_metadata = None
    ASSET_STORAGE_AVAILABLE = False


class UnifiedPDFExtractor:
    """Unified PDF extractor that processes documents directly using MinerU"""

    def __init__(self):
        # Redis configuration
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.redis_client = None
        self.http_client = None

        # Processing stats
        self.processed_count = 0
        self.error_count = 0
        self.retry_count = 0
        self.dlq_count = 0
        self.skipped_count = 0
        
        # Real-time metrics tracking
        self.processing_times = []  # List of recent processing times
        self.last_activity = None
        self.start_time = datetime.now()

        # Retry configuration
        self.max_retries = int(os.getenv("MAX_EXTRACTION_RETRIES", "3"))
        self.retry_delay_base = int(os.getenv("RETRY_DELAY_BASE_SECONDS", "60"))
        self.retry_delay_max = int(os.getenv("RETRY_DELAY_MAX_SECONDS", "3600"))

        # MinerU configuration
        self.mineru_container = os.getenv("MINERU_CONTAINER_NAME", "omotesamba-mineru")
        self.mineru_enabled = os.getenv("ENABLE_MINERU", "true").lower() == "true"

        # Internal status tracking (replaces external status tracker service)
        self.is_processing = False
        self.current_document = None
        self.started_at = None
        self.processing_lock = asyncio.Lock()

        # Asset storage
        self.asset_storage = get_asset_storage() if ASSET_STORAGE_AVAILABLE else None

        # Service controller for pause/resume functionality
        self.service_controller = None

    async def initialize(self):
        """Initialize connections and verify MinerU availability"""
        try:
            # Connect to Redis
            self.redis_client = await redis.from_url(self.redis_url, decode_responses=True)
            await self.redis_client.ping()
            logger.info(f"Connected to Redis at {self.redis_url}")

            # Initialize HTTP client for downloading URLs
            self.http_client = httpx.AsyncClient(timeout=300.0)

            # Initialize service controller for pause/resume
            if SERVICE_CONTROLLER_AVAILABLE:
                self.service_controller = SimpleServiceController("pdf_extractor")
                logger.info("✅ ServiceController initialized for pause/resume functionality")
            else:
                logger.warning("ServiceController not available - pause/resume disabled")

            # Check MinerU container status
            if self.mineru_enabled:
                await self._check_mineru_status()
            else:
                logger.warning("MinerU is disabled via ENABLE_MINERU environment variable")

        except Exception as e:
            logger.error(f"Failed to initialize: {e}")
            raise

    async def _store_metrics_in_redis(self):
        """Store current metrics in Redis for dashboard access"""
        try:
            current_time = datetime.now()
            hour_key = current_time.strftime("%Y-%m-%d-%H")
            
            # Store current processing stats
            await self.redis_client.hset("extraction_metrics:current", mapping={
                "processed_count": str(self.processed_count),
                "error_count": str(self.error_count),
                "retry_count": str(self.retry_count),
                "dlq_count": str(self.dlq_count),
                "last_activity": current_time.isoformat() if self.last_activity else "",
                "is_processing": str(self.is_processing),
                "current_document": self.current_document or "",
                "uptime_seconds": str(int((current_time - self.start_time).total_seconds()))
            })
            
            # Store processing times for average calculation (keep last 100)
            if self.processing_times:
                # Store as sorted set with timestamps
                for i, proc_time in enumerate(self.processing_times[-100:]):
                    timestamp = current_time.timestamp() - (100 - i)
                    await self.redis_client.zadd(
                        "extraction_metrics:processing_times", 
                        mapping={f"{proc_time}": timestamp}
                    )
                # Remove old entries (older than 1 hour)
                cutoff_time = current_time.timestamp() - 3600
                await self.redis_client.zremrangebyscore(
                    "extraction_metrics:processing_times", 0, cutoff_time
                )
            
            # Store hourly throughput
            await self.redis_client.hincrby(f"extraction_metrics:hourly:{hour_key}", "processed", 0)
            await self.redis_client.hincrby(f"extraction_metrics:hourly:{hour_key}", "failed", 0)
            await self.redis_client.expire(f"extraction_metrics:hourly:{hour_key}", 86400)  # 24 hours
            
        except Exception as e:
            logger.error(f"Error storing metrics in Redis: {e}")

    async def _update_processing_metrics(self, processing_time_seconds: float, success: bool):
        """Update processing metrics after document completion"""
        try:
            self.last_activity = datetime.now()
            
            if success:
                self.processed_count += 1
            else:
                self.error_count += 1
            
            # Store processing time (keep last 100 for rolling average)
            self.processing_times.append(processing_time_seconds)
            if len(self.processing_times) > 100:
                self.processing_times = self.processing_times[-100:]
            
            # Update hourly throughput in Redis
            hour_key = self.last_activity.strftime("%Y-%m-%d-%H")
            if success:
                await self.redis_client.hincrby(f"extraction_metrics:hourly:{hour_key}", "processed", 1)
            else:
                await self.redis_client.hincrby(f"extraction_metrics:hourly:{hour_key}", "failed", 1)
            
            # Store updated metrics in Redis
            await self._store_metrics_in_redis()
            
        except Exception as e:
            logger.error(f"Error updating processing metrics: {e}")

    async def get_current_metrics(self) -> dict:
        """Get current processing metrics for API endpoints"""
        try:
            # Calculate averages
            avg_processing_time = sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
            
            # Calculate throughput (docs per hour)
            current_hour = datetime.now().strftime("%Y-%m-%d-%H")
            hourly_processed = await self.redis_client.hget(f"extraction_metrics:hourly:{current_hour}", "processed") or 0
            hourly_failed = await self.redis_client.hget(f"extraction_metrics:hourly:{current_hour}", "failed") or 0
            
            # Calculate success rate
            total_attempts = self.processed_count + self.error_count
            success_rate = (self.processed_count / total_attempts * 100) if total_attempts > 0 else 100
            
            return {
                "total_documents": self.processed_count + self.error_count,
                "pending_count": 0,  # Will be filled from queue lengths
                "processing_count": 1 if self.is_processing else 0,
                "completed_count": self.processed_count,
                "failed_count": self.error_count,
                "avg_processing_time": avg_processing_time * 1000,  # Convert to milliseconds
                "extractors_active": 1 if self.is_processing else 0,
                "queue_size": 0,  # Will be filled from queue lengths
                "documents_per_hour": int(hourly_processed) if hourly_processed else 0,
                "success_rate": success_rate,
                "last_activity": self.last_activity.isoformat() if self.last_activity else None,
                "current_document": self.current_document,
                "uptime_seconds": int((datetime.now() - self.start_time).total_seconds())
            }
            
        except Exception as e:
            logger.error(f"Error getting current metrics: {e}")
            return {
                "total_documents": 0,
                "pending_count": 0,
                "processing_count": 0,
                "completed_count": 0,
                "failed_count": 0,
                "avg_processing_time": 0,
                "extractors_active": 0,
                "queue_size": 0,
                "documents_per_hour": 0,
                "success_rate": 100,
                "last_activity": None,
                "current_document": None,
                "uptime_seconds": 0
            }

    async def _check_mineru_available(self) -> bool:
        """Check if MinerU is available for processing (not busy)"""
        if self.is_processing:
            logger.info(f"MinerU is busy processing document: {self.current_document}")
            return False
        else:
            logger.debug("MinerU is available for processing")
            return True

    async def _notify_extraction_start(self, doc_id: str, estimated_duration: int = 300) -> bool:
        """Mark extraction as starting (internal state tracking)"""
        async with self.processing_lock:
            if self.is_processing:
                logger.warning(f"MinerU busy: already processing {self.current_document}")
                return False

            self.is_processing = True
            self.current_document = doc_id
            self.started_at = datetime.now().isoformat()
            logger.info(f"Started processing document: {doc_id}")
            return True

    async def _notify_extraction_complete(self, doc_id: str, success: bool, error: str = None):
        """Mark extraction as complete (internal state tracking)"""
        async with self.processing_lock:
            if self.current_document == doc_id:
                self.is_processing = False
                self.current_document = None
                self.started_at = None
                logger.info(f"Completed processing document: {doc_id} (success={success})")
            else:
                logger.warning(
                    f"Completion mismatch: expected {self.current_document}, got {doc_id}"
                )

    async def _check_mineru_status(self):
        """Check if MinerU container is available and healthy"""
        try:
            # Check if container exists and is running
            cmd = [
                "docker",
                "ps",
                "--filter",
                f"name={self.mineru_container}",
                "--format",
                "{{.Status}}",
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0 or not result.stdout.strip():
                logger.warning(
                    f"MinerU container '{self.mineru_container}' not found or not running"
                )
                self.mineru_enabled = False
                return

            # Check if MinerU command is available
            check_cmd = ["docker", "exec", self.mineru_container, "which", "mineru"]
            check_result = subprocess.run(check_cmd, capture_output=True, text=True)

            if check_result.returncode != 0:
                logger.warning("MinerU command not found in container")
                self.mineru_enabled = False
            else:
                logger.info(f"MinerU is available and ready in container '{self.mineru_container}'")
                self.mineru_enabled = True

        except Exception as e:
            logger.warning(f"Failed to check MinerU status: {e}")
            self.mineru_enabled = False

    def _is_pdf_url(self, url: str) -> bool:
        """Check if URL likely returns PDF content"""
        if not url:
            return False

        url_lower = url.lower()

        # Direct PDF files
        if url_lower.endswith(".pdf"):
            return True

        # EDINET API URLs - these ALWAYS return PDFs
        if "api.edinet-fsa.go.jp" in url_lower and "type=2" in url_lower:
            return True

        # TDNET PDF links - these ALWAYS return PDFs
        if "release.tdnet.info" in url_lower and "/inbs/" in url_lower:
            return True

        # BOJ PDF links - these are typically PDFs
        if "boj.or.jp" in url_lower and url_lower.endswith(".pdf"):
            return True

        # Other known PDF patterns
        pdf_patterns = [
            r"\.pdf\?",  # PDF with query parameters
            r"/pdf/",  # PDF in path
            r"format=pdf",  # PDF format parameter
            r"type=pdf",  # PDF type parameter
            r"output=pdf",  # PDF output parameter
        ]

        import re

        for pattern in pdf_patterns:
            if re.search(pattern, url_lower):
                return True

        # Check if it's clearly NOT a PDF based on file extension
        ext = self._get_file_extension_from_url(url)
        if ext and ext in {'xlsx', 'xls', 'docx', 'doc', 'pptx', 'ppt', 'html', 'htm', 'csv', 'json', 'xml', 'txt', 'md', 'zip', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'mp3', 'wav', 'ogg', 'm4a'}:
            return False
        
        # If no clear extension or could be PDF, assume it might be a PDF
        return True

    def _get_file_extension_from_url(self, url: str) -> str:
        """Extract file extension from URL"""
        try:
            # Parse URL and get path
            parsed = urlparse(url)
            path = unquote(parsed.path)
            
            # Get extension from path
            if '.' in path:
                return path.rsplit('.', 1)[1].lower()
            
            return ""
        except:
            return ""
    
    def _is_non_pdf_supported_file(self, url: str) -> bool:
        """Check if URL points to a non-PDF file that markitdown can process"""
        if not url:
            return False
            
        # Get file extension
        ext = self._get_file_extension_from_url(url)
        
        # Supported non-PDF extensions
        supported_extensions = {
            'xlsx', 'xls',      # Excel
            'docx', 'doc',      # Word
            'pptx', 'ppt',      # PowerPoint
            'html', 'htm',      # HTML
            'csv',              # CSV
            'json',             # JSON
            'xml',              # XML
            'txt',              # Text
            'md',               # Markdown
            'jpg', 'jpeg', 'png', 'gif', 'bmp',  # Images
            'mp3', 'wav', 'ogg', 'm4a',          # Audio
        }
        
        return ext in supported_extensions
    
    def _is_zip_file(self, url: str) -> bool:
        """Check if URL points to a ZIP file"""
        ext = self._get_file_extension_from_url(url)
        return ext == 'zip'

    def _is_retryable_error(self, error_msg: str) -> bool:
        """Determine if an error is retryable"""
        retryable_patterns = [
            "connection",
            "timeout",
            "service unavailable",
            "502",
            "503",
            "504",
            "container not found",
        ]

        non_retryable_patterns = [
            "file not found",
            "invalid pdf",
            "corrupted",
            "permission denied",
            "404",
        ]

        error_lower = error_msg.lower()

        # Check non-retryable first
        for pattern in non_retryable_patterns:
            if pattern in error_lower:
                return False

        # Check retryable patterns
        for pattern in retryable_patterns:
            if pattern in error_lower:
                return True

        return True  # Default to retryable

    def _calculate_retry_delay(self, retry_count: int) -> int:
        """Calculate exponential backoff delay in seconds"""
        delay = self.retry_delay_base * (2**retry_count)
        return min(delay, self.retry_delay_max)

    def _get_language_from_country(self, doc: Dict) -> str:
        """Get MinerU language parameter based on document country - comprehensive mapping"""
        if not doc:
            logger.warning("No document provided for language detection, defaulting to Japanese")
            return "japan"  # Default to Japanese for Japanese financial docs

        # Debug logging to trace country detection
        doc_id = doc.get("id", "unknown")
        logger.info(f"Language detection for doc {doc_id}: Full doc keys: {list(doc.keys())}")

        # Check multiple locations for country information
        country = ""

        # First try metadata.country (common in edinet/tdnet documents)
        metadata = doc.get("metadata", {})
        if isinstance(metadata, dict):
            country = metadata.get("country", "")
            logger.info(f"Doc {doc_id}: metadata.country = '{country}'")

        # Fallback to root level country
        if not country:
            country = doc.get("country", "")
            logger.info(f"Doc {doc_id}: root level country = '{country}'")

        # Try to infer country from source if still empty
        if not country and metadata:
            source = metadata.get("source", "")
            if source in ["edinet", "tdnet", "boj"]:
                country = "JP"  # Japanese sources
                logger.info(f"Doc {doc_id}: Inferred country 'JP' from source '{source}'")
            elif source in ["sec", "fed"]:
                country = "US"  # US sources
                logger.info(f"Doc {doc_id}: Inferred country 'US' from source '{source}'")

        # Additional fallback: try to infer from URL or title
        if not country:
            url = doc.get("url", "").lower()
            title = doc.get("title", "").lower()

            if any(jp_indicator in url or jp_indicator in title for jp_indicator in ["edinet", "tdnet", "boj", ".jp/"]):
                country = "JP"
                logger.info(f"Doc {doc_id}: Inferred country 'JP' from URL/title")
            elif any(us_indicator in url or us_indicator in title for us_indicator in ["sec.gov", "edgar", "fed", ".us/"]):
                country = "US"
                logger.info(f"Doc {doc_id}: Inferred country 'US' from URL/title")

        # Convert to uppercase for mapping
        country = country.upper() if country else ""

        # MinerU 2.1.0 comprehensive language mapping - FULL LANGUAGE SUPPORT ENABLED
        # Upgraded from 2.0.6 to support Portuguese, Spanish, French, German, Russian, Arabic
        country_language_map = {
            # Chinese variants - SUPPORTED
            "CN": "ch",  # China → Chinese (Simplified)
            "TW": "chinese_cht",  # Taiwan → Chinese (Traditional)
            "HK": "chinese_cht",  # Hong Kong → Chinese (Traditional)
            "MO": "chinese_cht",  # Macau → Chinese (Traditional)
            # Japanese - SUPPORTED
            "JP": "japan",  # Japan → Japanese
            # Korean - SUPPORTED
            "KR": "korean",  # South Korea → Korean
            "KP": "korean",  # North Korea → Korean
            # Portuguese - NEW in 2.1.0 (Critical for BCB documents!)
            "BR": "pt",  # Brazil → Portuguese (BCB central bank documents)
            "PT": "pt",  # Portugal → Portuguese
            "AO": "pt",  # Angola → Portuguese
            "MZ": "pt",  # Mozambique → Portuguese
            # Spanish - NEW in 2.1.0
            "ES": "es",  # Spain → Spanish
            "MX": "es",  # Mexico → Spanish
            "AR": "es",  # Argentina → Spanish
            "CL": "es",  # Chile → Spanish
            "CO": "es",  # Colombia → Spanish
            "PE": "es",  # Peru → Spanish
            # French - NEW in 2.1.0
            "FR": "fr",  # France → French
            "CA": "fr",  # Canada → French (Quebec)
            "BE": "fr",  # Belgium → French
            "CH": "fr",  # Switzerland → French
            # German - NEW in 2.1.0
            "DE": "de",  # Germany → German
            "AT": "de",  # Austria → German
            # Russian - NEW in 2.1.0
            "RU": "ru",  # Russia → Russian
            # Tamil-speaking regions - SUPPORTED
            "LK": "ta",  # Sri Lanka → Tamil
            # Arabic-speaking regions - NEW in 2.1.0
            "AE": "ar",  # UAE → Arabic
            "SA": "ar",  # Saudi Arabia → Arabic
            "EG": "ar",  # Egypt → Arabic
            # ALL OTHER COUNTRIES → No automatic fallback
        }

        # Get language from country - with fallback for unknown countries
        detected_lang = country_language_map.get(country)
        if detected_lang is None:
            logger.warning(f"Language mapping failed for country '{country}' in doc {doc_id}")
            logger.warning(f"Available countries: {list(country_language_map.keys())}")

            # Smart fallback based on document characteristics
            # Check if document title or URL suggests Japanese content
            title = doc.get("title", "")
            url = doc.get("url", "")

            if any(japanese_indicator in str(title).lower() or japanese_indicator in str(url).lower()
                   for japanese_indicator in ["edinet", "tdnet", "boj", ".jp/", "japan"]):
                logger.info(f"Doc {doc_id}: Defaulting to Japanese based on title/URL indicators")
                return "japan"

            # Check for CJK characters in title
            if any(0x3000 <= ord(c) <= 0x9FFF or 0xFF00 <= ord(c) <= 0xFFEF for c in title):
                logger.info(f"Doc {doc_id}: Defaulting to Japanese based on CJK characters in title")
                return "japan"

            # Default to English as last resort
            logger.info(f"Doc {doc_id}: Defaulting to English as last resort")
            return "en"

        # MinerU 2.1.0 supported languages - COMPREHENSIVE LANGUAGE SUPPORT
        # Significantly expanded language support compared to 2.0.6
        supported_in_210 = [
            # Original 2.0.6 languages
            "ch",
            "ch_server",
            "ch_lite",
            "en",
            "korean",
            "japan",
            "chinese_cht",
            "ta",
            "te",
            "ka",
            # NEW in 2.1.0 - Latin script languages
            "pt",  # Portuguese (Critical for BCB documents!)
            "es",  # Spanish
            "fr",  # French
            "de",  # German
            "ru",  # Russian
            "ar",  # Arabic
            # Additional languages may be available
        ]

        if detected_lang not in supported_in_210:
            raise ValueError(
                f"Language '{detected_lang}' for country '{country}' not supported by MinerU 2.1.0"
            )

        logger.info(f"Document from country '{country}' → language '{detected_lang}'")
        return detected_lang

    async def _extract_with_mineru(
        self, pdf_path: str, doc_id: str, is_cjk: bool = False, doc: Dict = None
    ) -> Dict:
        """Extract PDF content using MinerU in container"""
        extraction_started = False
        try:
            # Notify that we're starting extraction
            if not await self._notify_extraction_start(doc_id):
                return {
                    "success": False,
                    "error": "MinerU is busy with another document",
                    "method": "mineru_unified_busy",
                }

            extraction_started = True
            # Create temporary output directory
            with tempfile.TemporaryDirectory() as temp_dir:
                container_pdf_path = f"/tmp/{doc_id}_input.pdf"
                container_output_dir = f"/tmp/{doc_id}_output"

                # Copy PDF to container
                copy_cmd = [
                    "docker",
                    "cp",
                    pdf_path,
                    f"{self.mineru_container}:{container_pdf_path}",
                ]
                copy_result = subprocess.run(copy_cmd, capture_output=True, text=True)
                if copy_result.returncode != 0:
                    raise Exception(f"Failed to copy PDF to container: {copy_result.stderr}")

                # Run MinerU extraction
                mineru_cmd = [
                    "docker",
                    "exec",
                    self.mineru_container,
                    "mineru",
                    "-p",
                    container_pdf_path,
                    "-o",
                    container_output_dir,
                    "-b",
                    "vlm-sglang-client",  # Use sglang client backend
                    "-u",
                    "http://localhost:30000",  # Connect to sglang server locally in container
                    "-m",
                    "auto",  # Use auto detection method
                    "-l",
                    self._get_language_from_country(doc),
                ]

                logger.info(
                    f"⚡ MINERU EXTRACTION STARTING: {doc_id} | Language: {self._get_language_from_country(doc)} | Backend: vlm-sglang-client"
                )
                logger.info(f"🔧 MinerU command: {' '.join(mineru_cmd)}")
                process = await asyncio.create_subprocess_exec(
                    *mineru_cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
                )

                stdout, stderr = await process.communicate()

                logger.info(f"MinerU stdout: {stdout.decode()[:500]}")  # First 500 chars
                if stderr:
                    logger.warning(f"MinerU stderr: {stderr.decode()[:500]}")

                if process.returncode != 0:
                    logger.error(f"MinerU extraction failed: {stderr.decode()}")
                    raise Exception(f"MinerU extraction failed: {stderr.decode()}")

                # First check what's in the output directory
                ls_cmd = [
                    "docker",
                    "exec",
                    self.mineru_container,
                    "find",
                    container_output_dir,
                    "-type",
                    "f",
                ]
                ls_result = subprocess.run(ls_cmd, capture_output=True, text=True)
                logger.info(f"Files in container output dir: {ls_result.stdout}")

                # Copy output back from container - including subdirectories
                # Use proper docker cp syntax without shell=True to avoid issues
                copy_back_cmd = [
                    "docker",
                    "cp",
                    f"{self.mineru_container}:{container_output_dir}",
                    temp_dir,
                ]
                copy_back_result = subprocess.run(copy_back_cmd, capture_output=True, text=True)

                if copy_back_result.returncode != 0:
                    logger.error(f"Failed to copy output back: {copy_back_result.stderr}")
                else:
                    logger.info(f"Copied output back successfully for {doc_id}")

                # List what we got back
                output_files = list(Path(temp_dir).rglob("*"))
                logger.info(
                    f"Output files from MinerU for {doc_id}: {[str(f) for f in output_files]}"
                )

                # Read extraction results
                extracted_text = ""
                markdown_text = ""
                tables = []
                images = {}

                # Look for output files
                for file_path in Path(temp_dir).rglob("*"):
                    if file_path.is_file():
                        logger.info(
                            f"Found file: {file_path.name} ({file_path.stat().st_size} bytes)"
                        )
                        if file_path.suffix == ".md":
                            markdown_text = file_path.read_text(encoding="utf-8")
                            extracted_text = markdown_text  # Use markdown as main text
                            logger.info(f"Read markdown content: {len(markdown_text)} chars")
                        elif file_path.name.endswith("_content_list.json"):
                            with open(file_path, "r") as f:
                                content_data = json.load(f)
                                # Extract tables and other structured data
                                for item in content_data:
                                    if item.get("type") == "table":
                                        tables.append(item)

                # Clean up container files
                cleanup_cmd = [
                    "docker",
                    "exec",
                    self.mineru_container,
                    "rm",
                    "-rf",
                    container_pdf_path,
                    container_output_dir,
                ]
                subprocess.run(cleanup_cmd, capture_output=True)

                # Log successful extraction details
                logger.info(
                    f"✅ MINERU EXTRACTION SUCCESS: {doc_id} | Content: {len(extracted_text)} chars | Markdown: {len(markdown_text)} chars | Tables: {len(tables)}"
                )

                # Notify successful completion
                await self._notify_extraction_complete(doc_id, True)

                return {
                    "success": True,
                    "extracted_text": extracted_text,
                    "markdown": markdown_text,
                    "tables": tables,
                    "images": images,
                    "method": "mineru_unified",
                }

        except Exception as e:
            logger.error(f"MinerU extraction error: {e}")
            # Notify failed completion if we started
            if extraction_started:
                await self._notify_extraction_complete(doc_id, False, str(e))
            return {"success": False, "error": str(e), "method": "mineru_unified_failed"}

    async def _extract_with_markitdown(self, file_path: str, doc_id: str, doc: Dict = None) -> Dict:
        """Extract content from non-PDF files using MarkItDown"""
        try:
            if not MARKITDOWN_AVAILABLE:
                return {
                    "success": False,
                    "error": "MarkItDown not available - install with pip install markitdown[all]",
                    "method": "markitdown_unavailable",
                }
            
            # Initialize MarkItDown
            md = MarkItDown()
            
            # Extract content
            logger.info(f"📄 MARKITDOWN EXTRACTION STARTING: {doc_id} | File: {Path(file_path).name}")
            result = md.convert(file_path)
            
            if result and result.text_content:
                extracted_text = result.text_content.strip()
                text_length = len(extracted_text)
                
                logger.info(
                    f"✅ MARKITDOWN EXTRACTION SUCCESS: {doc_id} | Content: {text_length} chars"
                )
                
                return {
                    "success": True,
                    "extracted_text": extracted_text,
                    "markdown": extracted_text,  # MarkItDown returns markdown format
                    "tables": [],  # MarkItDown includes tables in markdown
                    "images": {},
                    "method": "markitdown",
                }
            else:
                return {
                    "success": False,
                    "error": "No content extracted from file",
                    "method": "markitdown_no_content",
                }
                
        except Exception as e:
            logger.error(f"MarkItDown extraction error: {e}")
            return {"success": False, "error": str(e), "method": "markitdown_failed"}

    async def _download_file(self, url: str, doc_id: str) -> Optional[bytes]:
        """Download file from URL with proper authentication (renamed from _download_pdf)"""
        try:
            headers = {}

            # Add EDINET authentication if needed
            if "api.edinet-fsa.go.jp" in url:
                edinet_key = os.environ.get("EDINET_KEY")
                if edinet_key:
                    headers["Subscription-Key"] = edinet_key

            response = await self.http_client.get(url, headers=headers)
            response.raise_for_status()
            return response.content

        except Exception as e:
            logger.error(f"Failed to download file from {url}: {e}")
            return None

    async def process_document(
        self, doc: Dict, is_retry: bool = False
    ) -> Tuple[Optional[Dict], str]:
        """Process a single document"""
        # Start timing for metrics
        process_start_time = datetime.now()

        # Defensive check: ensure doc is a dictionary
        if not isinstance(doc, dict):
            logger.error(f"process_document called with non-dict argument: {type(doc)}: {doc}")
            return None, "failed"

        doc_id = doc.get("id", "unknown")
        retry_count = doc.get("retry_count", 0)

        # Clear any previous extraction errors
        if "extraction_error" in doc:
            doc["extraction_error"] = ""

        try:
            # Check emergency stop
            if await check_emergency_stop("pdf_extractor"):
                logger.info(f"Emergency stop active, skipping {doc_id}")
                return doc, "skipped"

            # Check if MinerU is enabled
            if not self.mineru_enabled:
                logger.warning(f"MinerU disabled, skipping {doc_id}")
                doc["extraction_skipped"] = True
                doc["extraction_skip_reason"] = "mineru_disabled"
                return doc, "skipped"

            file_url = doc.get("url", "")

            # Determine file type and route accordingly
            if self._is_zip_file(file_url):
                # ZIP files: Send to DLQ for future implementation
                logger.warning(f"ZIP file detected: {file_url} - sending to DLQ for future implementation")
                doc["extraction_error"] = "ZIP file extraction not yet implemented - requires unzip and multi-file processing"
                doc["extraction_timestamp"] = datetime.now().isoformat()
                doc["extraction_method"] = "zip_not_implemented"
                return doc, "failed"
            
            elif self._is_non_pdf_supported_file(file_url):
                # Non-PDF files: Use markitdown
                logger.info(f"🔄 STARTING MARKITDOWN EXTRACTION: {doc_id} | URL: {file_url} | Attempt: {retry_count + 1}/{self.max_retries + 1}")
                
                # Track processing start time
                doc["processing_started_at"] = datetime.now().isoformat()
                
                # Determine appropriate file extension
                ext = self._get_file_extension_from_url(file_url)
                if not ext:
                    ext = "unknown"
                
                # Download or read file
                with tempfile.NamedTemporaryFile(suffix=f".{ext}", delete=False) as temp_file:
                    if file_url.startswith("/"):
                        # Local file
                        if not Path(file_url).exists():
                            logger.error(f"File not found: {file_url}")
                            return doc, "failed"
                        temp_file.write(Path(file_url).read_bytes())
                    else:
                        # Download from URL
                        file_content = await self._download_file(file_url, doc_id)
                        if not file_content:
                            return doc, "retry" if retry_count < self.max_retries else "failed"
                        temp_file.write(file_content)

                    temp_file.flush()

                    # Extract with markitdown
                    result = await self._extract_with_markitdown(temp_file.name, doc_id, doc)
                    
                    # Clean up temp file
                    Path(temp_file.name).unlink(missing_ok=True)
                
            elif self._is_pdf_url(file_url):
                # PDF files: Use MinerU
                logger.info(f"🔄 STARTING MINERU EXTRACTION: {doc_id} | URL: {file_url} | Attempt: {retry_count + 1}/{self.max_retries + 1}")
                
                # Track processing start time
                doc["processing_started_at"] = datetime.now().isoformat()

                # Download or read PDF
                with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
                    if file_url.startswith("/"):
                        # Local file
                        if not Path(file_url).exists():
                            logger.error(f"File not found: {file_url}")
                            return doc, "failed"
                        temp_file.write(Path(file_url).read_bytes())
                    else:
                        # Download from URL
                        pdf_content = await self._download_file(file_url, doc_id)
                        if not pdf_content:
                            return doc, "retry" if retry_count < self.max_retries else "failed"
                        temp_file.write(pdf_content)

                    temp_file.flush()

                    # Check for CJK content
                    check_cmd = ["pdftotext", "-enc", "UTF-8", temp_file.name, "-"]
                    check_result = subprocess.run(check_cmd, capture_output=True, text=True)

                    has_cjk = False
                    if check_result.returncode == 0:
                        text_sample = check_result.stdout[:1000]
                        has_cjk = any(
                            0x3000 <= ord(c) <= 0x9FFF or 0xFF00 <= ord(c) <= 0xFFEF
                            for c in text_sample
                        )

                    # Extract with MinerU
                    result = await self._extract_with_mineru(temp_file.name, doc_id, has_cjk, doc)

                    # Clean up temp file
                    Path(temp_file.name).unlink(missing_ok=True)
            
            else:
                # Unknown file type
                logger.warning(f"Unknown file type for URL: {file_url}")
                doc["extraction_error"] = f"Unknown file type for URL: {file_url}"
                doc["extraction_timestamp"] = datetime.now().isoformat()
                doc["extraction_method"] = "unknown_file_type"
                return doc, "failed"

            # Process extraction results (common for both MinerU and markitdown)
            if result["success"]:
                extracted_text = result["extracted_text"]
                text_length = len(extracted_text.strip())

                # Validate extracted content length
                if text_length < 150:
                    logger.warning(
                        f"Document {doc_id} has insufficient content ({text_length} chars), moving to DLQ"
                    )
                    doc["extracted_text"] = extracted_text
                    doc["extraction_method"] = result["method"]
                    doc["extraction_timestamp"] = datetime.now().isoformat()
                    doc[
                        "extraction_error"
                    ] = f"Insufficient content extracted: {text_length} characters (minimum 150 required)"
                    doc["extraction_metadata"] = {
                        "has_tables": len(result.get("tables", [])) > 0,
                        "table_count": len(result.get("tables", [])),
                        "has_cjk": result.get("has_cjk", False),
                        "text_length": text_length,
                        "insufficient_content": True,
                    }
                    return doc, "failed"

                # Add extraction results to document
                doc["extracted_text"] = extracted_text
                doc["extraction_method"] = result["method"]
                doc["extraction_timestamp"] = datetime.now().isoformat()
                doc["extraction_metadata"] = {
                    "has_tables": len(result.get("tables", [])) > 0,
                    "table_count": len(result.get("tables", [])),
                    "has_cjk": result.get("has_cjk", False),
                    "text_length": text_length,
                }

                if result.get("markdown"):
                    doc["extracted_markdown"] = result["markdown"]

                # Store assets if available
                if self.asset_storage and (result.get("tables") or result.get("images")):
                    # Implementation for asset storage would go here
                    pass

                # Calculate processing time for enhanced logging
                processing_start = doc.get("processing_started_at")
                if processing_start:
                    try:
                        start_time = datetime.fromisoformat(processing_start)
                        processing_duration = (datetime.now() - start_time).total_seconds()
                        logger.info(
                            f"🎉 DOCUMENT EXTRACTION COMPLETED: {doc_id} | Processing Time: {processing_duration:.2f}s | Content: {text_length} chars | Method: {result['method']}"
                        )
                    except:
                        logger.info(
                            f"🎉 DOCUMENT EXTRACTION COMPLETED: {doc_id} | Content: {text_length} chars | Method: {result['method']}"
                        )
                else:
                    logger.info(
                        f"🎉 DOCUMENT EXTRACTION COMPLETED: {doc_id} | Content: {text_length} chars | Method: {result['method']}"
                    )
                
                # Update metrics for successful processing
                processing_time = (datetime.now() - process_start_time).total_seconds()
                await self._update_processing_metrics(processing_time, success=True)
                
                return doc, "success"
            else:
                # Extraction failed
                error_msg = result.get("error", "Unknown error")
                doc["extraction_error"] = error_msg
                doc["last_attempt_at"] = datetime.now().isoformat()

                if self._is_retryable_error(error_msg) and retry_count < self.max_retries:
                    return doc, "retry"
                else:
                    # Update metrics for failed processing
                    processing_time = (datetime.now() - process_start_time).total_seconds()
                    await self._update_processing_metrics(processing_time, success=False)
                    return doc, "failed"

        except Exception as e:
            logger.error(f"Error processing document {doc_id}: {e}")
            doc["extraction_error"] = str(e)
            doc["last_attempt_at"] = datetime.now().isoformat()

            if retry_count < self.max_retries:
                return doc, "retry"
            else:
                # Update metrics for failed processing (exception case)
                processing_time = (datetime.now() - process_start_time).total_seconds()
                await self._update_processing_metrics(processing_time, success=False)
                return doc, "failed"

    async def _route_document(self, doc: Dict, status: str):
        """Route document to appropriate queue based on status"""
        # Defensive check: ensure doc is a dictionary
        if not isinstance(doc, dict):
            logger.error(f"_route_document called with non-dict argument: {type(doc)}: {doc}")
            return

        doc_id = doc.get("id", "unknown")

        if status == "success":
            # Push to extraction_completed_queue for validation gate 1
            await self.redis_client.lpush("extraction_completed_queue", json.dumps(doc))
            self.processed_count += 1

            # Enhanced logging to show the actual queue flow
            logger.info(f"✅ Document {doc_id} extracted successfully → extraction_completed_queue")
            logger.debug(
                "Pipeline flow: extraction_queue → PDF extractor → extraction_completed_queue → Pipeline Validator → preprocessing_queue"
            )

        elif status == "retry":
            retry_count = doc.get("retry_count", 0)
            doc["retry_count"] = retry_count + 1
            doc["retry_after"] = (
                datetime.now() + timedelta(seconds=self._calculate_retry_delay(retry_count))
            ).isoformat()

            await self.redis_client.lpush("extraction_retry_queue", json.dumps(doc))
            self.retry_count += 1
            logger.warning(f"🔄 Document {doc_id} scheduled for retry")

        elif status == "failed":
            doc["failed_at"] = datetime.now().isoformat()
            doc["final_status"] = "permanently_failed"

            await self.redis_client.lpush("extraction_failed_dlq", json.dumps(doc))
            self.dlq_count += 1
            logger.error(f"💀 Document {doc_id} sent to DLQ")

        elif status == "skipped":
            # Put back in extraction queue for later processing
            await self.redis_client.lpush("extraction_queue", json.dumps(doc))
            self.skipped_count += 1
            logger.info(f"⏭️  Document {doc_id} skipped (emergency stop or MinerU disabled)")

    async def process_retry_queue(self):
        """Process documents from retry queue that are ready"""
        try:
            # Get all documents from retry queue
            retry_docs = []
            while True:
                result = await self.redis_client.rpop("extraction_retry_queue")
                if not result:
                    break

                # Handle JSON deserialization safely
                try:
                    if isinstance(result, str):
                        doc = json.loads(result)

                        # Check for double-encoded JSON (common issue with Redis queues)
                        if isinstance(doc, str):
                            logger.debug(
                                "Double-encoded JSON detected in retry queue, parsing again..."
                            )
                            doc = json.loads(doc)
                    else:
                        doc = result

                    if isinstance(doc, dict):
                        retry_docs.append(doc)
                    else:
                        logger.error(
                            f"Invalid retry document format: expected dict, got {type(doc)}: {doc}"
                        )
                        continue

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse retry document JSON: {e}, raw data: {result}")
                    continue

            ready_docs = []
            not_ready_docs = []
            current_time = datetime.now()

            for doc in retry_docs:
                retry_after_str = doc.get("retry_after")
                if retry_after_str:
                    retry_after = datetime.fromisoformat(retry_after_str)
                    if current_time >= retry_after:
                        ready_docs.append(doc)
                    else:
                        not_ready_docs.append(doc)
                else:
                    ready_docs.append(doc)

            # Put not-ready documents back
            for doc in not_ready_docs:
                await self.redis_client.lpush("extraction_retry_queue", json.dumps(doc))

            # Process ready documents
            for doc in ready_docs:
                logger.info(f"🔄 Processing retry document {doc.get('id', 'unknown')}")
                processed_doc, status = await self.process_document(doc, is_retry=True)
                if processed_doc:
                    await self._route_document(processed_doc, status)

        except Exception as e:
            logger.error(f"Error processing retry queue: {e}")

    async def process_single_document(self):
        """Process a single document from the extraction queue"""
        try:
            # Check if already processing
            if self.is_processing:
                return {
                    "status": "skipped",
                    "message": "Already processing a document",
                    "current_document": self.current_document
                }

            # Check if MinerU is available
            if not await self._check_mineru_available():
                return {
                    "status": "error",
                    "message": "MinerU is not available"
                }

            # Get one document from the queue
            result = await self.redis_client.rpop("extraction_queue")
            if not result:
                return {
                    "status": "no_documents",
                    "message": "No documents in extraction queue"
                }

            # Parse document JSON
            try:
                if isinstance(result, str):
                    doc = json.loads(result)
                    if isinstance(doc, str):
                        doc = json.loads(doc)  # Handle double-encoded JSON
                else:
                    doc = result

                if not isinstance(doc, dict):
                    return {
                        "status": "error",
                        "message": f"Invalid document format: {type(doc)}"
                    }
                    
            except json.JSONDecodeError as e:
                return {
                    "status": "error",
                    "message": f"Failed to parse document JSON: {e}"
                }

            # Process the document
            self.current_document = doc.get("id", "unknown")
            processed_doc, status = await self.process_document(doc)

            if processed_doc:
                await self._route_document(processed_doc, status)
                return {
                    "status": "success",
                    "message": f"Successfully processed document {self.current_document}",
                    "document_id": self.current_document,
                    "processing_status": status
                }
            else:
                return {
                    "status": "failed",
                    "message": f"Failed to process document {self.current_document}",
                    "document_id": self.current_document
                }

        except Exception as e:
            logger.error(f"Error in process_single_document: {e}")
            return {
                "status": "error",
                "message": f"Processing failed: {str(e)}"
            }

    async def run(self):
        """Main processing loop with ServiceController integration"""
        logger.info("🎯 PDF EXTRACTOR: Background queue consumer started - monitoring for documents")
        print("🎯 PDF EXTRACTOR: Background queue consumer started - monitoring for documents")
        logger.info("Starting unified PDF extractor...")
        logger.info(
            f"Configuration: max_retries={self.max_retries}, mineru_enabled={self.mineru_enabled}"
        )
        logger.info(
            "Pipeline: extraction_queue → PDF extractor → preprocessing_queue → categorizer"
        )

        await self.initialize()

        # Start queue processing with pause/resume support
        if self.service_controller:
            logger.info("✅ Dashboard pause/resume is functional")
            await self._queue_processing_loop()
        else:
            # Run without service controller (legacy mode)
            logger.warning("⚠️ Running without ServiceController - dashboard pause/resume disabled")
            await self._queue_processing_loop()

    async def _queue_processing_loop(self):
        """The actual queue processing loop - can be controlled by ServiceController"""
        while True:
            try:
                # Check emergency stop and service status
                if await check_emergency_stop("pdf_extractor"):
                    logger.info(
                        "PDF extractor is disabled or emergency stop is active - pausing for 60 seconds..."
                    )
                    await asyncio.sleep(60)
                    continue

                # Check pause/resume status from dashboard
                if self.service_controller:
                    if not await self.service_controller.check_should_process():
                        logger.debug("Service paused via dashboard - waiting 5 seconds...")
                        await asyncio.sleep(5)
                        continue

                # Check MinerU status periodically
                if not self.mineru_enabled:
                    await self._check_mineru_status()
                    if not self.mineru_enabled:
                        logger.info("MinerU not available - waiting 60 seconds...")
                        await asyncio.sleep(60)
                        continue

                # Process retry queue first
                await self.process_retry_queue()

                # Check if MinerU is available before getting a document
                if not await self._check_mineru_available():
                    logger.debug("MinerU is busy, waiting 5 seconds before checking again...")
                    await asyncio.sleep(5)
                    continue

                # Process new documents
                result = await self.redis_client.brpop("extraction_queue", timeout=10)

                if not result:
                    continue

                # Parse document
                _, doc_json = result

                # Handle JSON deserialization with proper error checking
                try:
                    if isinstance(doc_json, str):
                        doc = json.loads(doc_json)

                        # Check for double-encoded JSON (common issue with Redis queues)
                        if isinstance(doc, str):
                            logger.debug("Double-encoded JSON detected, parsing again...")
                            doc = json.loads(doc)
                    else:
                        # If it's already a dict (shouldn't happen with Redis), use it directly
                        doc = doc_json

                    # Ensure doc is actually a dictionary
                    if not isinstance(doc, dict):
                        logger.error(
                            f"Invalid document format: expected dict, got {type(doc)}: {doc}"
                        )
                        continue

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse document JSON: {e}, raw data: {doc_json}")
                    continue

                # Double-check MinerU availability before processing
                if not await self._check_mineru_available():
                    # Put document back in queue and continue
                    await self.redis_client.lpush("extraction_queue", doc_json)
                    logger.info("MinerU became busy, returning document to queue")
                    await asyncio.sleep(2)
                    continue

                # Process the document
                processed_doc, status = await self.process_document(doc)

                if processed_doc:
                    await self._route_document(processed_doc, status)

                # Log progress
                if (self.processed_count + self.error_count) % 10 == 0:
                    logger.info(
                        f"Progress: {self.processed_count} processed, "
                        f"{self.retry_count} retried, {self.dlq_count} failed, "
                        f"{self.skipped_count} skipped"
                    )

            except asyncio.CancelledError:
                logger.info(
                    "🔴 PDF EXTRACTOR: Background queue consumer stopped - no longer monitoring for documents"
                )
                print(
                    "🔴 PDF EXTRACTOR: Background queue consumer stopped - no longer monitoring for documents"
                )
                break
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in queue: {e}")
                self.error_count += 1
            except Exception as e:
                import traceback

                logger.error(f"Error in processing loop: {e}")
                logger.error(f"Full traceback: {traceback.format_exc()}")
                self.error_count += 1
                await asyncio.sleep(5)

    async def cleanup(self):
        """Clean up resources"""
        if self.service_controller:
            await self.service_controller.shutdown()
        if self.redis_client:
            await self.redis_client.close()
        if self.http_client:
            await self.http_client.aclose()


async def main():
    """Main entry point"""
    extractor = UnifiedPDFExtractor()

    try:
        await extractor.run()
    except KeyboardInterrupt:
        logger.info("Shutting down...")
    finally:
        await extractor.cleanup()
        logger.info(
            f"Final stats: {extractor.processed_count} processed, "
            f"{extractor.retry_count} retried, {extractor.dlq_count} failed, "
            f"{extractor.skipped_count} skipped"
        )


if __name__ == "__main__":
    asyncio.run(main())
