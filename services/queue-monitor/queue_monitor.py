#!/usr/bin/env python3
"""
Containerized queue monitoring with metrics and alerting
"""

import asyncio
import json
import logging
import os
from datetime import datetime

import redis.asyncio as redis
from prometheus_client import Counter, Gauge, start_http_server
from rich.console import Console
from rich.live import Live
from rich.table import Table

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Prometheus metrics
queue_depth = Gauge("redis_queue_depth", "Current queue depth", ["queue_name"])
total_items = Gauge("redis_total_items", "Total items across all queues")
backup_trigger_count = Counter("redis_backup_triggers", "Number of backup triggers", ["reason"])
queue_processing_rate = Gauge(
    "redis_queue_processing_rate", "Items processed per minute", ["queue_name"]
)


class QueueMonitor:
    def __init__(self):
        self.redis_host = os.getenv("REDIS_HOST", "redis")
        self.redis_port = int(os.getenv("REDIS_PORT", "6379"))
        self.backup_threshold = int(os.getenv("BACKUP_THRESHOLD", "1000"))
        self.alert_threshold = int(os.getenv("ALERT_THRESHOLD", "5000"))
        self.polling_interval = float(os.getenv("POLLING_INTERVAL", "0.5"))  # Default to 500ms
        self.console = Console()
        self.previous_counts = {}
        self.redis = None

        self.queues = [
            "document_discovery_queue",
            "extraction_completed_queue",
            "categorization_completed_queue",
            "analysis_completed_queue",
            "validation_completed_queue",
            "storage_completed_queue",
        ]

    async def connect(self):
        """Connect to Redis"""
        self.redis = await redis.from_url(f"redis://{self.redis_host}:{self.redis_port}")
        logger.info(f"Connected to Redis at {self.redis_host}:{self.redis_port}")

    async def get_queue_stats(self):
        """Get current queue statistics"""
        stats = {}
        total = 0

        for queue in self.queues:
            try:
                length = await self.redis.llen(queue)
                stats[queue] = length
                total += length

                # Update Prometheus metrics
                queue_depth.labels(queue_name=queue).set(length)

                # Calculate processing rate
                if queue in self.previous_counts:
                    rate = self.previous_counts[queue] - length
                    queue_processing_rate.labels(queue_name=queue).set(abs(rate))

                self.previous_counts[queue] = length

            except Exception as e:
                logger.error(f"Error getting stats for {queue}: {e}")
                stats[queue] = -1

        total_items.set(total)
        return stats, total

    def create_status_table(self, stats, total):
        """Create a rich table for status display"""
        table = Table(title=f"Queue Monitor - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        table.add_column("Queue", style="cyan", no_wrap=True)
        table.add_column("Depth", style="magenta", justify="right")
        table.add_column("Status", style="green")
        table.add_column("Progress", style="blue")

        for queue, count in stats.items():
            if count == -1:
                table.add_row(queue, "ERROR", "❌", "")
                continue

            # Status indicator
            if count > self.alert_threshold:
                status = "🚨 CRITICAL"
                style = "red"
            elif count > self.backup_threshold:
                status = "⚠️  HIGH"
                style = "yellow"
            else:
                status = "✅ OK"
                style = "green"

            # Progress bar
            bar_length = min(30, count // 100)
            progress = "█" * bar_length

            table.add_row(queue.replace("_queue", ""), f"{count:,}", status, progress, style=style)

        table.add_row("", "", "", "", style="dim")
        table.add_row("TOTAL", f"{total:,}", "", "", style="bold")

        return table

    async def check_alerts(self, stats, total):
        """Check if any alerts should be triggered with proper deduplication"""
        alerts = []

        # Check individual queue thresholds
        for queue, count in stats.items():
            if count > self.alert_threshold:
                alert_key = f"queue_depth_high:{queue}"
                alert_message = f"🚨 {queue} exceeds alert threshold: {count:,} items"

                # Check if alert already exists
                if await self._should_create_or_update_alert(alert_key, count):
                    await self._create_or_update_alert(
                        alert_key=alert_key,
                        alert_type="queue_depth_high",
                        queue_name=queue,
                        current_value=count,
                        threshold_value=self.alert_threshold,
                        message=alert_message,
                    )
                    backup_trigger_count.labels(reason="alert_threshold").inc()

                alerts.append(alert_message)
            else:
                # Check if we need to resolve an existing alert
                alert_key = f"queue_depth_high:{queue}"
                await self._resolve_alert_if_exists(alert_key)

        # Check total threshold
        if total > self.alert_threshold * 2:
            alert_key = "total_queue_depth_critical"
            alert_message = f"🚨 Total items critical: {total:,}"

            if await self._should_create_or_update_alert(alert_key, total):
                await self._create_or_update_alert(
                    alert_key=alert_key,
                    alert_type="total_queue_depth_critical",
                    queue_name="all_queues",
                    current_value=total,
                    threshold_value=self.alert_threshold * 2,
                    message=alert_message,
                )
                backup_trigger_count.labels(reason="total_threshold").inc()

            alerts.append(alert_message)
        else:
            # Resolve total queue alert if it exists
            await self._resolve_alert_if_exists("total_queue_depth_critical")

        # Log alerts
        for alert in alerts:
            logger.warning(alert)

        return alerts

    async def _should_create_or_update_alert(self, alert_key: str, current_value: int) -> bool:
        """Check if we should create a new alert or update an existing one"""
        try:
            # Check if alert already exists
            existing_alert_data = await self.redis.hget("active_alerts", alert_key)

            if existing_alert_data:
                existing_alert = json.loads(existing_alert_data)
                # Update alert with new values
                existing_alert["actual_value"] = current_value
                existing_alert["last_updated"] = datetime.now().isoformat()
                existing_alert["occurrence_count"] = existing_alert.get("occurrence_count", 1) + 1

                # Save updated alert
                await self.redis.hset("active_alerts", alert_key, json.dumps(existing_alert))
                return False  # Don't create new alert, just updated existing
            else:
                return True  # Create new alert
        except Exception as e:
            logger.error(f"Error checking existing alert {alert_key}: {e}")
            return True  # Create alert on error

    async def _create_or_update_alert(
        self,
        alert_key: str,
        alert_type: str,
        queue_name: str,
        current_value: int,
        threshold_value: int,
        message: str,
    ):
        """Create or update an alert with deduplication"""
        try:
            alert_data = {
                "id": alert_key,
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "alert_type": alert_type,
                "severity": "critical" if current_value > threshold_value * 2 else "warning",
                "service": "queue-monitor",
                "metric_name": "queue_depth",
                "threshold_value": threshold_value,
                "actual_value": current_value,
                "queue_name": queue_name,
                "message": message,
                "resolved_at": None,
                "notification_sent": False,
                "occurrence_count": 1,
                "metadata": {
                    "queue_name": queue_name,
                    "current_value": current_value,
                    "threshold_value": threshold_value,
                },
            }

            # Store alert in active alerts hash (for deduplication)
            await self.redis.hset("active_alerts", alert_key, json.dumps(alert_data))

            # Also add to alert queue for processing (only for new alerts)
            existing_alert = await self.redis.hget("active_alerts", alert_key)
            if not existing_alert or json.loads(existing_alert).get("occurrence_count", 0) == 1:
                await self.redis.lpush("alert_queue", json.dumps(alert_data))

            logger.info(f"Created/updated alert: {alert_key}")

        except Exception as e:
            logger.error(f"Error creating/updating alert {alert_key}: {e}")

    async def _resolve_alert_if_exists(self, alert_key: str):
        """Resolve an alert if it exists and is active"""
        try:
            existing_alert_data = await self.redis.hget("active_alerts", alert_key)

            if existing_alert_data:
                existing_alert = json.loads(existing_alert_data)

                # Only resolve if not already resolved
                if existing_alert.get("resolved_at") is None:
                    existing_alert["resolved_at"] = datetime.now().isoformat()
                    existing_alert["last_updated"] = datetime.now().isoformat()

                    # Update the alert as resolved
                    await self.redis.hset("active_alerts", alert_key, json.dumps(existing_alert))

                    # Remove from active alerts after marking as resolved
                    await self.redis.hdel("active_alerts", alert_key)

                    logger.info(f"Resolved alert: {alert_key}")

        except Exception as e:
            logger.error(f"Error resolving alert {alert_key}: {e}")

    async def monitor_loop(self):
        """Main monitoring loop"""
        logger.info("Starting queue monitor...")

        with Live(console=self.console, refresh_per_second=1) as live:
            while True:
                try:
                    stats, total = await self.get_queue_stats()
                    table = self.create_status_table(stats, total)

                    # Check for alerts
                    alerts = await self.check_alerts(stats, total)
                    if alerts:
                        for alert in alerts:
                            table.add_row("", alert, "", "", style="red bold")

                    # Add system info
                    table.add_row("", "", "", "", style="dim")
                    table.add_row(
                        "Backup Threshold",
                        f"{self.backup_threshold:,}",
                        f"Alert: {self.alert_threshold:,}",
                        "",
                        style="dim",
                    )

                    live.update(table)

                    # Sleep for configured interval
                    await asyncio.sleep(self.polling_interval)

                except KeyboardInterrupt:
                    logger.info("Monitor stopped by user")
                    break
                except Exception as e:
                    logger.error(f"Monitor error: {e}")
                    await asyncio.sleep(10)

    async def health_check_handler(self, request):
        """Simple health check endpoint"""
        return {"status": "healthy", "timestamp": datetime.now().isoformat()}

    async def run(self):
        """Run the monitor"""
        # Start Prometheus metrics server
        start_http_server(8090)
        logger.info("Prometheus metrics available on :8090/metrics")

        # Connect to Redis
        await self.connect()

        # Run monitoring loop
        await self.monitor_loop()

        # Cleanup
        if self.redis:
            await self.redis.close()


if __name__ == "__main__":
    monitor = QueueMonitor()
    asyncio.run(monitor.run())
