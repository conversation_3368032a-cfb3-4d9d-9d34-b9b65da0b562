#!/usr/bin/env python3
"""
ABOUTME: Add v3.0.0 categorization prompt using Docker PostgreSQL
ABOUTME: Connects to PostgreSQL on port 5433
"""

import asyncio
import os
import sys

# Add parent directory to path to import from services
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Copy the entire prompt and hierarchy from the fixed version
from add_categorization_v3_fixed import (
    CATEGORIZATION_PROMPT_V3,
    format_hierarchy_for_prompt,
    get_hierarchy_stats,
)
from prompt_manager.prompt_manager import PromptManager


async def add_v3_categorization():
    """Add version 3.0.0 of categorization prompt"""

    # Database configuration for Docker PostgreSQL
    db_config = {
        "host": os.getenv("DB_HOST", "localhost"),
        "port": os.getenv("DB_PORT", 5433),  # Docker PostgreSQL port
        "database": os.getenv("DB_NAME", "omotesamba"),
        "user": os.getenv("DB_USER", "postgres"),
        "password": os.getenv("DB_PASSWORD", "postgres"),
    }

    pm = PromptManager(db_config)

    try:
        await pm.initialize()
        print("✅ Connected to database")

        # Format hierarchy and get stats
        hierarchy_text = format_hierarchy_for_prompt()
        primary, secondary, tertiary = get_hierarchy_stats()

        # Create prompt with hierarchy
        full_prompt = CATEGORIZATION_PROMPT_V3.replace("{category_list}", hierarchy_text)

        # Create version 3.0.0
        version_id = await pm.create_prompt_version(
            template_name="categorization",  # Single generic name
            version="3.0.0",
            prompt_text=full_prompt,
            system_prompt="You are an expert financial document categorizer with deep understanding of global financial markets and regulations. Always output categories in lowercase.",
            parameters={
                "model": "gemini-2.5-flash",
                "temperature": 0.2,
                "max_tokens": 1024,
                "response_format": "json",
                "hierarchy_levels": 3,
                "primary_categories": primary,
                "secondary_categories": secondary,
                "tertiary_categories": tertiary,
                "output_language": "english",
                "case_sensitivity": "lowercase",
                "features": [
                    "3_tier_hierarchy",
                    "universal_categories",
                    "english_only_output",
                    "title_parsing",
                    "id_extraction",
                    "date_extraction",
                    "translation",
                    "lowercase_categories",
                ],
            },
            changelog="""Version 3.0.0 - 3-Tier Universal Categorization
Major Changes:
- Introduced 3-tier hierarchy (primary → secondary → tertiary)
- Secondary categories are now more generic (e.g., "securitization" instead of specific types)
- Tertiary categories provide specific document types
- Better organization for analysis prompts (can use secondary level)
- Template name simplified to "categorization" for automatic versioning
- Fixed: All categories must be lowercase for system compatibility

Statistics:
- 8 primary categories
- ~40 secondary categories
- ~200 tertiary categories
- Examples: receivables_certificate → securitization → receivables_certificate_annual_report

Benefits:
- More logical grouping of related documents
- Easier to add new document types
- Analysis prompts can target secondary categories
- Maintains all v2.0.0 improvements (English output, field parsing, etc.)

Testing:
- Tested on 10 diverse documents with 100% success rate
- Receivables certificates properly categorized under debt_credit → securitization
- All output in English with proper field extraction""",
            make_active=True,
        )

        print("\n✅ Successfully added categorization prompt v3.0.0")
        print("   Template: categorization")
        print(f"   Version ID: {version_id}")
        print("   Status: Active")
        print(f"   Hierarchy: {primary} primary → {secondary} secondary → {tertiary} tertiary")
        print("   Case: All categories lowercase")
        print("\n📊 Example categorization path:")
        print("   Document: 'Receivables Certificate Annual Report'")
        print("   Primary: debt_credit")
        print("   Secondary: securitization")
        print("   Tertiary: receivables_certificate_annual_report")

    except Exception as e:
        print(f"❌ Error: {e}")
        raise
    finally:
        await pm.close()


if __name__ == "__main__":
    asyncio.run(add_v3_categorization())
