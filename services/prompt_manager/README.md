# Prompt Manager Service

Advanced prompt versioning and management system for Omotesamba financial intelligence platform.

## Overview

The Prompt Manager service provides sophisticated prompt versioning, template management, and analytics for AI-powered document analysis. It integrates with pydantic-ai for structured outputs and supports multiple AI models with load balancing.

## Key Features

### 🗂️ **Database-backed Prompt Management**
- PostgreSQL storage with full ACID compliance
- Semantic versioning (v1.0.0, v1.1.0, v2.0.0)
- Template categories and descriptions
- Changelog tracking for all prompt versions

### 🤖 **Pydantic-AI Integration**
- Structured outputs with automatic validation
- Type-safe AI interactions
- Error handling and retry logic
- Model-agnostic prompt execution

### ⚖️ **Multi-Model Support**
- **50/50 load balancing** between Gemini 2.5 Pro and DeepSeek v3
- Consistent document routing (same document always uses same model)
- Automatic fallback if one model is unavailable
- Cost optimization across providers

### 📊 **Usage Analytics**
- Real-time cost tracking per prompt and model
- Performance metrics (execution time, success rate)
- Token usage monitoring
- Historical analytics and trending

### 🎯 **Intelligent Routing**
- Document source-based prompt selection
- Content-aware template routing
- Fallback mechanisms for unknown document types
- Template performance optimization

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Document      │    │     Prompt      │    │   Structured    │
│   Metadata      │───▶│    Manager      │───▶│     Output      │
│                 │    │                 │    │   (Pydantic)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   PostgreSQL    │
                       │   (Prompts &    │
                       │   Analytics)    │
                       └─────────────────┘
```

## Sophisticated Prompts

### 1. **TDNET Earnings Analysis** (3,321 characters)
```json
{
  "template_name": "analysis_tdnet_earnings",
  "purpose": "Hedge fund-grade financial analysis of Japanese earnings reports",
  "outputs": {
    "earnings_results": "Revenue, profit, margins with YoY changes",
    "trading_analysis": "BUY/HOLD/SELL with price targets",
    "market_impact": "Direction, magnitude, affected sectors"
  }
}
```

### 2. **BOJ Policy Analysis** (3,948 characters)
```json
{
  "template_name": "analysis_boj_policy",
  "purpose": "Central bank policy analysis for FX and rates trading",
  "outputs": {
    "policy_decisions": "Rate changes, forward guidance",
    "market_implications": "JPY, equity, rates impact",
    "trading_recommendations": "Specific FX and rates trades"
  }
}
```

### 3. **Document Categorization** (781 characters)
```json
{
  "template_name": "categorization_flash",
  "purpose": "Fast document routing and classification",
  "outputs": {
    "document_type": "earnings/policy/regulatory/other",
    "specialized_template": "Recommended analysis template",
    "processing_priority": "1-10 urgency scoring"
  }
}
```

### 4. **Default Analysis** (869 characters)
```json
{
  "template_name": "analysis_default",
  "purpose": "Fallback for unspecialized document types",
  "outputs": {
    "executive_summary": "Key takeaways",
    "trading_recommendation": "Action with confidence",
    "market_impact": "Direction and magnitude"
  }
}
```

## API Documentation

### Authentication
No authentication required for internal service communication.

### Endpoints

#### **GET /health**
Health check endpoint.

```bash
curl http://localhost:8020/health
```

#### **GET /api/v1/templates**
List all prompt templates.

```bash
curl http://localhost:8020/api/v1/templates
```

Response:
```json
[
  {
    "template_name": "analysis_tdnet_earnings",
    "description": "TDNET earnings analysis with hedge fund metrics",
    "category": "analysis"
  }
]
```

#### **GET /api/v1/templates/{template_name}/active**
Get the active version of a prompt template.

```bash
curl http://localhost:8020/api/v1/templates/analysis_tdnet_earnings/active
```

#### **GET /api/v1/analytics/usage**
Get usage analytics and performance metrics.

```bash
curl "http://localhost:8020/api/v1/analytics/usage?hours=24"
```

#### **POST /api/v1/templates**
Create a new prompt template.

```bash
curl -X POST http://localhost:8020/api/v1/templates \
  -H "Content-Type: application/json" \
  -d '{
    "template_name": "analysis_new_type",
    "description": "Analysis for new document type",
    "category": "analysis"
  }'
```

## Deployment

### Docker Compose

The service is included in the main docker-compose.yml:

```yaml
prompt-manager:
  build:
    context: ./services/prompt_manager
    dockerfile: Dockerfile
  container_name: omotesamba-prompt-manager
  profiles: ["ai", "analysis", "all"]
  environment:
    - DATABASE_URL=*************************************************/omotesamba
    - GEMINI_API_KEY=${GEMINI_API_KEY}
    - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
  ports:
    - "8020:8020"
  depends_on:
    postgres:
      condition: service_healthy
```

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `DATABASE_URL` | Yes | PostgreSQL connection string |
| `GEMINI_API_KEY` | Yes | Google Gemini API key |
| `DEEPSEEK_API_KEY` | No | DeepSeek API key (enables 50/50 load balancing) |

### Database Setup

The service automatically creates required tables on startup:

- `prompt_templates` - Template metadata
- `prompt_versions` - Versioned prompts
- `prompt_usage_log` - Analytics and usage tracking

## Development

### Local Development

```bash
# Install dependencies
cd services/prompt_manager
uv pip install -r requirements.txt

# Start PostgreSQL (via docker-compose)
docker-compose up postgres -d

# Run the service
python prompt_api.py
```

### Testing

```bash
# Run the test script
python ../../scripts/test_prompt_system.py

# Test individual endpoints
curl http://localhost:8020/health
curl http://localhost:8020/api/v1/templates
```

### Adding New Prompts

1. **Register Template**:
```python
await prompt_manager.register_prompt_template(
    "analysis_new_type",
    "Analysis for new document type",
    "analysis"
)
```

2. **Create Version**:
```python
await prompt_manager.create_prompt_version(
    template_name="analysis_new_type",
    version="1.0.0",
    prompt_text=your_prompt_text,
    system_prompt="You are a financial analyst...",
    changelog="Initial version"
)
```

3. **Update Routing Logic**:
Add routing logic in `versioned_queue_consumer.py`:
```python
def _get_template_name(self, message: Dict[str, Any]) -> str:
    source = message.get("metadata", {}).get("source", "")
    if source == "new_source":
        return "analysis_new_type"
    # ... existing logic
```

## Monitoring and Analytics

### Key Metrics

- **Prompt Usage**: Executions per template
- **Model Distribution**: Gemini vs DeepSeek usage
- **Performance**: Average execution time
- **Cost Tracking**: Token usage and costs
- **Success Rate**: Successful vs failed executions

### Logs

Service logs include:

- Prompt migrations and updates
- AI model execution results
- Database connection events
- Error details and stack traces

### Dashboard Integration

The prompt manager exposes metrics for integration with monitoring dashboards:

```bash
# Get real-time analytics
curl "http://localhost:8020/api/v1/analytics/usage?hours=1"
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify DATABASE_URL format
   - Ensure database exists

2. **AI Model Errors**
   - Verify API keys are set
   - Check model quotas and limits
   - Review prompt text length

3. **Prompt Not Found**
   - Check template is registered
   - Verify active prompt version exists
   - Review migration logs

### Debug Commands

```bash
# Check service health
curl http://localhost:8020/health

# View container logs
docker logs omotesamba-prompt-manager

# Check database tables
docker exec -it omotesamba-postgres psql -U omotesamba -d omotesamba -c "\dt"

# Test database connection
docker exec -it omotesamba-postgres psql -U omotesamba -d omotesamba -c "SELECT COUNT(*) FROM prompt_templates;"
```

## Integration with Pro Analyzer

The versioned pro analyzer (`services/pro_analyzer/versioned_queue_consumer.py`) automatically uses this prompt manager:

1. **Document arrives** in `categorization_completed_queue`
2. **Router determines** appropriate template based on source/content
3. **Prompt Manager** loads template and executes with pydantic-ai
4. **Structured output** validated and sent to `analysis_completed_queue`
5. **Analytics logged** for monitoring and optimization

This replaces the basic string-based prompts with sophisticated, versioned, trackable prompts that produce consistent, validated outputs.
