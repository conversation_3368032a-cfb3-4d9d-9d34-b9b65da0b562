#!/bin/bash

# Script to fix confidence scores in categorization_quality_dlq documents
# Uses Redis CLI and jq to process documents

echo "Starting DLQ confidence score fix..."

# Check if jq is available
if ! command -v jq &> /dev/null; then
    echo "Error: jq is required but not installed"
    exit 1
fi

# Get the current length of the DLQ
DLQ_LENGTH=$(docker exec omotesamba-redis redis-cli llen categorization_quality_dlq)
echo "Found $DLQ_LENGTH documents in categorization_quality_dlq"

if [ "$DLQ_LENGTH" -eq 0 ]; then
    echo "No documents to process"
    exit 0
fi

PROCESSED_COUNT=0
FIXED_COUNT=0

# Process all documents in the DLQ
while [ "$PROCESSED_COUNT" -lt "$DLQ_LENGTH" ]; do
    # Pop a document from the right end of the DLQ
    DOCUMENT=$(docker exec omotesamba-redis redis-cli rpop categorization_quality_dlq)
    
    if [ -z "$DOCUMENT" ] || [ "$DOCUMENT" = "(nil)" ]; then
        echo "No more documents to process"
        break
    fi
    
    PROCESSED_COUNT=$((PROCESSED_COUNT + 1))
    
    # Check if the document has a confidence score less than 1.0
    CONFIDENCE=$(echo "$DOCUMENT" | jq -r '.categorization.confidence // empty')
    
    if [ -n "$CONFIDENCE" ] && [ "$CONFIDENCE" != "null" ]; then
        # Check if confidence is less than 1.0 (indicating it needs fixing)
        if (( $(echo "$CONFIDENCE < 1.0" | bc -l) )); then
            # Fix the confidence by multiplying by 100
            NEW_CONFIDENCE=$(echo "$CONFIDENCE * 100" | bc -l)
            
            # Update the document with the fixed confidence
            FIXED_DOCUMENT=$(echo "$DOCUMENT" | jq ".categorization.confidence = $NEW_CONFIDENCE")
            
            # Send the fixed document to categorization_completed_queue
            echo "$FIXED_DOCUMENT" | docker exec -i omotesamba-redis redis-cli lpush categorization_completed_queue
            
            FIXED_COUNT=$((FIXED_COUNT + 1))
            echo "Document $PROCESSED_COUNT: Fixed confidence $CONFIDENCE -> $NEW_CONFIDENCE"
        else
            # Confidence is already correct, return to DLQ
            echo "$DOCUMENT" | docker exec -i omotesamba-redis redis-cli lpush categorization_quality_dlq
            echo "Document $PROCESSED_COUNT: Confidence already correct ($CONFIDENCE), returned to DLQ"
        fi
    else
        # No confidence score found, return to DLQ
        echo "$DOCUMENT" | docker exec -i omotesamba-redis redis-cli lpush categorization_quality_dlq
        echo "Document $PROCESSED_COUNT: No confidence score found, returned to DLQ"
    fi
done

echo ""
echo "Processing complete:"
echo "- Total documents processed: $PROCESSED_COUNT"
echo "- Documents fixed and requeued: $FIXED_COUNT"
echo "- Documents returned to DLQ: $((PROCESSED_COUNT - FIXED_COUNT))"

# Check final queue lengths
FINAL_DLQ_LENGTH=$(docker exec omotesamba-redis redis-cli llen categorization_quality_dlq)
COMPLETED_QUEUE_LENGTH=$(docker exec omotesamba-redis redis-cli llen categorization_completed_queue)

echo ""
echo "Final queue lengths:"
echo "- categorization_quality_dlq: $FINAL_DLQ_LENGTH"
echo "- categorization_completed_queue: $COMPLETED_QUEUE_LENGTH"

echo "Done!"
