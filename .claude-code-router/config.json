{"Providers": [{"name": "deepseek", "api_base_url": "https://api.deepseek.com/chat/completions", "api_key": "***********************************", "models": ["deepseek-chat", "deepseek-reasoner"], "transformer": {"use": ["deepseek"], "deepseek-chat": {"use": ["tooluse"]}}}, {"name": "gemini", "api_base_url": "https://generativelanguage.googleapis.com/v1beta/models/", "api_key": "sk-xxx", "models": ["gemini-2.5-flash", "gemini-2.5-pro"], "transformer": {"use": ["gemini"]}}, {"name": "volcengine", "api_base_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "api_key": "sk-xxx", "models": ["deepseek-v3-250324", "deepseek-r1-250528"], "transformer": {"use": ["deepseek"]}}, {"name": "kimi", "api_base_url": "https://api.moonshot.ai/v1/chat/completions", "api_key": "sk-Dg3s6LYrz7Pi6dSLylMM6ZWH98BlzBB0QvafXx0qk9hpR5wz", "models": ["kimi-k2-0711-preview"]}, {"name": "ollama", "api_base_url": "http://localhost:11434/v1/chat/completions", "api_key": "ollama", "models": ["qwen2.5-coder:latest"]}], "Router": {"default": "kimi,kimi-k2-0711-preview"}}