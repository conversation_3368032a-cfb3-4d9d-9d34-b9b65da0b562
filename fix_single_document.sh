#!/bin/bash

# Fix the single document in the DLQ by multiplying confidence by 100

echo "Fixing the document in categorization_quality_dlq..."

# Get the document from the DLQ
DOCUMENT=$(docker exec omotesamba-redis redis-cli rpop categorization_quality_dlq)

if [ -z "$DOCUMENT" ] || [ "$DOCUMENT" = "(nil)" ]; then
    echo "No documents in DLQ"
    exit 0
fi

echo "Original document retrieved from DLQ"

# Extract the current confidence value
CONFIDENCE=$(echo "$DOCUMENT" | jq -r '.categorization.confidence // empty')

if [ -n "$CONFIDENCE" ] && [ "$CONFIDENCE" != "null" ]; then
    echo "Current confidence: $CONFIDENCE"

    # Check if confidence is less than 1.0 (indicating it needs fixing)
    if (( $(echo "$CONFIDENCE < 1.0" | bc -l) )); then
        # Fix the confidence by multiplying by 100
        NEW_CONFIDENCE=$(echo "$CONFIDENCE * 100" | bc -l)

        # Update the document with the fixed confidence
        FIXED_DOCUMENT=$(echo "$DOCUMENT" | jq ".categorization.confidence = $NEW_CONFIDENCE")

        # Send the fixed document to categorization_completed_queue
        echo "$FIXED_DOCUMENT" | docker exec -i omotesamba-redis redis-cli -x lpush categorization_completed_queue

        echo "Document fixed: confidence $CONFIDENCE -> $NEW_CONFIDENCE"
        echo "Document sent to categorization_completed_queue"
    else
        # Confidence is already correct, return to DLQ
        echo "$DOCUMENT" | docker exec -i omotesamba-redis redis-cli -x lpush categorization_quality_dlq
        echo "Confidence already correct ($CONFIDENCE), returned to DLQ"
    fi
else
    # No confidence score found, return to DLQ
    echo "$DOCUMENT" | docker exec -i omotesamba-redis redis-cli -x lpush categorization_quality_dlq
    echo "No confidence score found, returned to DLQ"
fi

# Check final queue lengths
FINAL_DLQ_LENGTH=$(docker exec omotesamba-redis redis-cli llen categorization_quality_dlq)
COMPLETED_QUEUE_LENGTH=$(docker exec omotesamba-redis redis-cli llen categorization_completed_queue)

echo ""
echo "Final queue lengths:"
echo "- categorization_quality_dlq: $FINAL_DLQ_LENGTH"
echo "- categorization_completed_queue: $COMPLETED_QUEUE_LENGTH"

echo "Done!"