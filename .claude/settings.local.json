{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(python:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(awk:*)", "<PERSON><PERSON>(chmod:*)", "Bash(PGPASSWORD=development psql -h localhost -U omotesamba -d omotesamba -c \"\\dt\")", "Bash(PGPASSWORD=development psql -h localhost -U omotesamba -d omotesamba -c \"SELECT title, source_type, created_at FROM document_analysis ORDER BY created_at DESC LIMIT 5;\")", "Bash(PGPASSWORD=development psql -h localhost -U omotesamba -d omotesamba -c \"\\d document_analysis\")", "Bash(PGPASSWORD=development psql -h localhost -U omotesamba -d postgres -c \"\\du\")", "Bash(docker logs:*)", "Bash(ls:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(nvidia-smi:*)", "Bash(ps:*)", "Bash(kill:*)", "Bash(dpkg:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(minikube start:*)", "Bash(kubectl get:*)", "Bash(kubectl config:*)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:github.com)", "WebFetch(domain:arxiv.org)", "Bash(docker build:*)", "<PERSON><PERSON>(docker:*)", "Bash(systemctl:*)", "Bash(./scripts/install_dolphin.sh:*)", "<PERSON><PERSON>(source:*)", "Bash(uv pip install:*)", "Bash(./start_vllm_dolphin.sh:*)", "Bash(kubectl exec:*)", "<PERSON><PERSON>(timeout:*)", "Bash(streamlit run:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(pdfinfo:*)", "Bash(echo $PATH)", "Bash(node:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(direnv allow:*)", "Bash(pip install:*)", "Bash(pipx install:*)", "Bash(nvm use:*)", "Bash(npm config set:*)", "<PERSON><PERSON>(curl:*)", "Bash(# Copy missing Dockerfiles\ncp docker/Dockerfile.rss-monitor service-scrapers/dockerfiles/\ncp docker/Dockerfile.pipeline-c service-extractor/dockerfiles/ 2>/dev/null || echo \"Pipeline-C dockerfile not found\"\n\n# Check for MinerU dockerfiles\nls docker/Dockerfile.mineru* 2>/dev/null || echo \"No MinerU dockerfiles found in docker/\")", "Bash(git add:*)", "Bash(just processes \"python\")", "Bash(fd:*)", "Bash(http POST localhost:8015/categorize text=\"Toyota announced record earnings for Q3\" --json)", "Bash(npx promptfoo eval:*)", "Bash(fd:*)", "Bash(usql:*)", "Bash(rg:*)", "<PERSON><PERSON>(watch:*)", "Bash(git fsck:*)", "Bash(git gc:*)", "Bash(git reset:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(convert:*)", "Bash(cp:*)", "<PERSON><PERSON>(true)", "Bash(redis-cli:*)", "<PERSON><PERSON>(env)", "Bash(POSTGRES_HOST=localhost POSTGRES_PORT=5433 REDIS_HOST=localhost REDIS_PORT=6380 python3 api/document_api.py)", "Bash(./run_documents_api_local.sh:*)", "Bash(redis-cli:*)", "<PERSON><PERSON>(uv run:*)", "Bash(--gpus all )", "Bash(--shm-size 32gb )", "<PERSON>sh(--ipc host )", "Bash(--ulimit memlock=-1 )", "Bash(--ulimit stack=67108864 )", "Bash(-p 30000:30000 )", "Bash(-p 8000:8000 )", "Bash(-p 7860:7860 )", "Bash(-e MINERU_MODEL_SOURCE=local )", "Bash(-e MINERU_DEVICE=cuda )", "Bash(-e CUDA_VISIBLE_DEVICES=0 )", "Bash(-e FORCE_CUDA=1 )", "Bash(-e PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True )", "<PERSON><PERSON>(mineru-japanese:latest)", "<PERSON><PERSON>(cat:*)", "Bash(# Process EDINET document 1: 滋賀銀行\ndocker cp \"/home/<USER>/dev/pair/omotesamba/test_documents/edinet/2025-06-27/S100W6CA_株式会社滋賀銀行_臨時報告書.pdf\" omotesamba-mineru:/tmp/edinet1.pdf\n\n# Extract it\ndocker exec omotesamba-mineru mineru -p /tmp/edinet1.pdf -o /tmp/edinet1_output -b vlm-sglang-client -u http://localhost:30000\n\n# Copy results back\ndocker cp omotesamba-mineru:/tmp/edinet1_output /home/<USER>/dev/pair/omotesamba/extraction_results/edinet/shiga_bank/)", "Bash(# Copy original PDF to results\ncp \"/home/<USER>/dev/pair/omotesamba/test_documents/edinet/2025-06-27/S100W6CA_株式会社滋賀銀行_臨時報告書.pdf\" /home/<USER>/dev/pair/omotesamba/extraction_results/edinet/shiga_bank/\n\n# Process EDINET document 2: マンダム\ndocker cp \"/home/<USER>/dev/pair/omotesamba/test_documents/edinet/2025-06-27/S100W6RM_株式会社マンダム_臨時報告書.pdf\" omotesamba-mineru:/tmp/edinet2.pdf\ndocker exec omotesamba-mineru mineru -p /tmp/edinet2.pdf -o /tmp/edinet2_output -b vlm-sglang-client -u http://localhost:30000)", "Bash(# Copy Mandom results\ndocker cp omotesamba-mineru:/tmp/edinet2_output /home/<USER>/dev/pair/omotesamba/extraction_results/edinet/mandom/\ncp \"/home/<USER>/dev/pair/omotesamba/test_documents/edinet/2025-06-27/S100W6RM_株式会社マンダム_臨時報告書.pdf\" /home/<USER>/dev/pair/omotesamba/extraction_results/edinet/mandom/\n\n# Now process TDNET documents\n# TDNET document 1: 資本コストや株価を意識した経営\ndocker cp \"/home/<USER>/dev/pair/omotesamba/test_documents/tdnet/********/13010_1163545_資本コストや株価を意識した経営の実現に向けた対応.pdf\" omotesamba-mineru:/tmp/tdnet1.pdf\ndocker exec omotesamba-mineru mineru -p /tmp/tdnet1.pdf -o /tmp/tdnet1_output -b vlm-sglang-client -u http://localhost:30000)", "Bash(# Copy TDNET results\ndocker cp omotesamba-mineru:/tmp/tdnet1_output /home/<USER>/dev/pair/omotesamba/extraction_results/tdnet/capital_cost_management/\ncp \"/home/<USER>/dev/pair/omotesamba/test_documents/tdnet/********/13010_1163545_資本コストや株価を意識した経営の実現に向けた対応.pdf\" /home/<USER>/dev/pair/omotesamba/extraction_results/tdnet/capital_cost_management/\n\n# TDNET document 2: 譲渡制限付株式報酬\ndocker cp \"/home/<USER>/dev/pair/omotesamba/test_documents/tdnet/********/160A0_1163547_譲渡制限付株式報酬としての新株式の発行に関するお知らせ.pdf\" omotesamba-mineru:/tmp/tdnet2.pdf\ndocker exec omotesamba-mineru mineru -p /tmp/tdnet2.pdf -o /tmp/tdnet2_output -b vlm-sglang-client -u http://localhost:30000)", "Bash(# Copy final TDNET results\ndocker cp omotesamba-mineru:/tmp/tdnet2_output /home/<USER>/dev/pair/omotesamba/extraction_results/tdnet/restricted_stock/\ncp \"/home/<USER>/dev/pair/omotesamba/test_documents/tdnet/********/160A0_1163547_譲渡制限付株式報酬としての新株式の発行に関するお知らせ.pdf\" /home/<USER>/dev/pair/omotesamba/extraction_results/tdnet/restricted_stock/\n\n# Check what we have extracted\necho \"=== Extraction Results Overview ===\"\nfind /home/<USER>/dev/pair/omotesamba/extraction_results -name \"*.md\" -o -name \"*.pdf\" | sort)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 reprocess_extracted_documents.py --dry-run --limit 10)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 reprocess_extracted_documents.py --dry-run --no-sample)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 reprocess_storage_documents.py --dry-run)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 reprocess_storage_documents.py)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 reprocess_categorization_documents.py --dry-run)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 reprocess_categorization_documents.py)", "Bash(tar:*)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 reprocess_all_extraction_completed.py --dry-run)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 reprocess_all_extraction_completed.py --no-sample)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\nimport json\n\nasync def check_queues():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    # Check all relevant queues\n    queues = [\n        'extraction_completed_queue',\n        'document_discovery_queue', \n        'global_processing_queue',\n        'recovery_processing_queue',\n        'regeneration_queue'\n    ]\n    \n    print('=== Current Queue Status ===')\n    for queue in queues:\n        size = await r.llen(queue)\n        print(f'{queue}: {size} documents')\n        \n        # Show sample if not empty\n        if size > 0:\n            doc_json = await r.lindex(queue, 0)\n            if doc_json:\n                doc = json.loads(doc_json)\n                doc_id = doc.get('id', 'unknown')\n                url = doc.get('url', '')[:80] + '...' if doc.get('url') else 'No URL'\n                print(f'  Sample: ID={doc_id}, URL={url}')\n    \n    await r.aclose()\n\nasyncio.run(check_queues())\n\")", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\nimport json\n\nasync def analyze_discovery_queue():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    size = await r.llen('document_discovery_queue')\n    print(f'\\\\n=== Document Discovery Queue Analysis ===')\n    print(f'Total documents: {size}')\n    \n    # Analyze document types\n    stats = {\n        'pdf': 0,\n        'tdnet': 0,\n        'edinet': 0,\n        'boj': 0,\n        'reprocessing': 0,\n        'has_extraction_issues': 0\n    }\n    \n    # Check first 100 documents\n    for i in range(min(100, size)):\n        doc_json = await r.lindex('document_discovery_queue', i)\n        if doc_json:\n            doc = json.loads(doc_json)\n            url = doc.get('url', '')\n            \n            if url.lower().endswith('.pdf'):\n                stats['pdf'] += 1\n            \n            if 'tdnet' in url.lower():\n                stats['tdnet'] += 1\n            elif 'edinet' in url.lower():\n                stats['edinet'] += 1\n            elif 'boj' in url.lower():\n                stats['boj'] += 1\n                \n            if doc.get('reprocessing'):\n                stats['reprocessing'] += 1\n                \n            if doc.get('reprocessing_reason') == 'extraction_issues_batch_fix':\n                stats['has_extraction_issues'] += 1\n    \n    print(f'\\\\nStatistics (from first 100):')\n    for key, value in stats.items():\n        print(f'  {key}: {value}')\n        \n    # Check if these are the documents we're looking for\n    doc_json = await r.lindex('document_discovery_queue', 0)\n    if doc_json:\n        doc = json.loads(doc_json)\n        print(f'\\\\nFirst document details:')\n        print(f'  ID: {doc.get(\\\"id\\\")}')\n        print(f'  Reprocessing: {doc.get(\\\"reprocessing\\\", False)}')\n        print(f'  Reprocessing reason: {doc.get(\\\"reprocessing_reason\\\", \\\"N/A\\\")}')\n        print(f'  Original method: {doc.get(\\\"original_extraction_method\\\", \\\"N/A\\\")}')\n    \n    await r.aclose()\n\nasyncio.run(analyze_discovery_queue())\n\")", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\nimport json\n\nasync def check_reprocessed_docs():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    discovery_size = await r.llen('document_discovery_queue')\n    print(f'Total documents in discovery queue: {discovery_size}')\n    \n    # Check documents from different positions to find reprocessed ones\n    reprocessed_count = 0\n    sample_shown = False\n    \n    for i in range(min(100, discovery_size)):\n        doc_json = await r.lindex('document_discovery_queue', i)\n        if doc_json:\n            doc = json.loads(doc_json)\n            if doc.get('reprocessing') and doc.get('reprocessing_reason') == 'extraction_issues_batch_fix':\n                reprocessed_count += 1\n                \n                if not sample_shown:\n                    print(f'\\\\nSample reprocessed document at index {i}:')\n                    print(f'  ID: {doc.get(\\\"id\\\")}')\n                    print(f'  URL: {doc.get(\\\"url\\\", \\\"\\\")[:80]}...')\n                    print(f'  Reprocessing: {doc.get(\\\"reprocessing\\\")}')\n                    print(f'  Reason: {doc.get(\\\"reprocessing_reason\\\")}')\n                    print(f'  Original method: {doc.get(\\\"original_extraction_method\\\")}')\n                    print(f'  Timestamp: {doc.get(\\\"reprocessing_timestamp\\\")}')\n                    sample_shown = True\n    \n    print(f'\\\\nFound {reprocessed_count} reprocessed documents in first 100 entries')\n    \n    # Also check from the middle of the queue where our docs likely are\n    middle_start = discovery_size - 1000\n    for i in range(middle_start, min(middle_start + 100, discovery_size)):\n        doc_json = await r.lindex('document_discovery_queue', i)\n        if doc_json:\n            doc = json.loads(doc_json)\n            if doc.get('reprocessing') and doc.get('reprocessing_reason') == 'extraction_issues_batch_fix':\n                print(f'\\\\nFound reprocessed document at index {i}:')\n                print(f'  ID: {doc.get(\\\"id\\\")}')\n                print(f'  URL: {doc.get(\\\"url\\\", \\\"\\\")[:80]}...')\n                break\n    \n    await r.aclose()\n\nasyncio.run(check_reprocessed_docs())\n\")", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\nimport json\nfrom collections import Counter\n\nasync def check_duplicates():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    queue_size = await r.llen('document_discovery_queue')\n    print(f'Total documents in queue: {queue_size}')\n    \n    # Collect all document IDs\n    doc_ids = []\n    urls = []\n    \n    for i in range(queue_size):\n        doc_json = await r.lindex('document_discovery_queue', i)\n        if doc_json:\n            doc = json.loads(doc_json)\n            doc_id = doc.get('id', f'unknown_{i}')\n            url = doc.get('url', '')\n            doc_ids.append(doc_id)\n            urls.append(url)\n    \n    # Count duplicates\n    id_counts = Counter(doc_ids)\n    url_counts = Counter(urls)\n    \n    duplicate_ids = [(id, count) for id, count in id_counts.items() if count > 1]\n    duplicate_urls = [(url, count) for url, count in url_counts.items() if count > 1 and url]\n    \n    print(f'\\\\nUnique document IDs: {len(id_counts)}')\n    print(f'Duplicate IDs found: {len(duplicate_ids)}')\n    \n    if duplicate_ids:\n        print('\\\\nDuplicate IDs:')\n        for doc_id, count in duplicate_ids[:10]:  # Show first 10\n            print(f'  {doc_id}: {count} times')\n    \n    print(f'\\\\nUnique URLs: {len([u for u in url_counts if u])}')\n    print(f'Duplicate URLs found: {len(duplicate_urls)}')\n    \n    if duplicate_urls:\n        print('\\\\nDuplicate URLs:')\n        for url, count in duplicate_urls[:10]:  # Show first 10\n            print(f'  {url[:80]}...: {count} times')\n    \n    await r.aclose()\n\nasyncio.run(check_duplicates())\n\")", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\nimport json\n\nasync def analyze_queue():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    queue_size = await r.llen('document_discovery_queue')\n    print(f'Current queue size: {queue_size} unique documents')\n    \n    # Analyze document types\n    stats = {\n        'pdf': 0,\n        'tdnet': 0,\n        'edinet': 0,\n        'boj': 0,\n        'bcb': 0,\n        'test': 0,\n        'reprocessing_batch': 0,\n        'japanese_upgrade': 0\n    }\n    \n    for i in range(queue_size):\n        doc_json = await r.lindex('document_discovery_queue', i)\n        if doc_json:\n            doc = json.loads(doc_json)\n            url = doc.get('url', '')\n            doc_id = doc.get('id', '')\n            \n            if url.lower().endswith('.pdf'):\n                stats['pdf'] += 1\n            \n            if 'tdnet' in doc_id.lower():\n                stats['tdnet'] += 1\n            elif 'edinet' in doc_id.lower():\n                stats['edinet'] += 1\n            elif 'boj' in doc_id.lower():\n                stats['boj'] += 1\n            elif 'bcb' in doc_id.lower():\n                stats['bcb'] += 1\n            elif 'test' in doc_id.lower():\n                stats['test'] += 1\n                \n            if doc.get('reprocessing_reason') == 'extraction_issues_batch_fix':\n                stats['reprocessing_batch'] += 1\n            elif doc.get('reprocessing_reason') == 'mineru_japanese_upgrade':\n                stats['japanese_upgrade'] += 1\n    \n    print('\\\\nDocument breakdown:')\n    for key, value in stats.items():\n        print(f'  {key}: {value}')\n    \n    await r.aclose()\n\nasyncio.run(analyze_queue())\n\")", "Bash(REDIS_URL=redis://localhost:6380 MINERU_SERVICE_URL=http://localhost:8015 uv run python3 queue_based_extractor.py)", "Bash(REDIS_URL=redis://localhost:6380 MINERU_SERVICE_URL=http://localhost:8015 ASSET_STORAGE_PATH=/tmp/extracted_assets uv run python3 queue_based_extractor.py)", "Bash(REDIS_URL=redis://localhost:6380 MINERU_SERVICE_URL=http://localhost:30000 ASSET_STORAGE_PATH=/tmp/extracted_assets uv run python3 queue_based_extractor.py 2 >& 1)", "Bash(REDIS_URL=redis://localhost:6380 PDF_API_URL=http://localhost:8015 uv run python3 process_documents_simple.py --max 5)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\nimport json\n\nasync def check_extraction_results():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    size = await r.llen('extraction_completed_queue')\n    print(f'Total documents in extraction_completed_queue: {size}')\n    \n    # Check a few samples\n    print('\\\\nSample documents:')\n    for i in range(min(5, size)):\n        doc_json = await r.lindex('extraction_completed_queue', i)\n        if doc_json:\n            doc = json.loads(doc_json)\n            doc_id = doc.get('id', 'unknown')\n            method = doc.get('extraction_method', 'unknown')\n            text_len = len(doc.get('extracted_text', ''))\n            has_error = 'extraction_error' in doc\n            \n            print(f'\\\\n{i+1}. {doc_id}')\n            print(f'   Method: {method}')\n            print(f'   Text length: {text_len}')\n            print(f'   Has error: {has_error}')\n            if has_error:\n                print(f'   Error: {doc.get(\\\"extraction_error\\\", \\\"\\\")}')\n    \n    await r.aclose()\n\nasyncio.run(check_extraction_results())\n\")", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\nimport json\n\nasync def find_pdf_documents():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    # Check recovery queue for failed PDFs\n    recovery_size = await r.llen('recovery_processing_queue')\n    print(f'Documents in recovery_processing_queue: {recovery_size}')\n    \n    if recovery_size > 0:\n        print('\\\\nRecovery queue samples:')\n        for i in range(min(5, recovery_size)):\n            doc_json = await r.lindex('recovery_processing_queue', i)\n            if doc_json:\n                doc = json.loads(doc_json)\n                doc_id = doc.get('id', 'unknown')\n                url = doc.get('url', '')[:80] + '...'\n                retry_count = doc.get('retry_count', 0)\n                print(f'{i+1}. {doc_id} (retries: {retry_count})')\n                print(f'   URL: {url}')\n    \n    # Look for PDFs in extraction_completed that might need reprocessing\n    completed_size = await r.llen('extraction_completed_queue')\n    pdf_count = 0\n    failed_count = 0\n    \n    print(f'\\\\nScanning {completed_size} documents in extraction_completed_queue...')\n    \n    for i in range(completed_size):\n        doc_json = await r.lindex('extraction_completed_queue', i)\n        if doc_json:\n            doc = json.loads(doc_json)\n            url = doc.get('url', '')\n            \n            if url.lower().endswith('.pdf'):\n                pdf_count += 1\n                if doc.get('extraction_error') or doc.get('extraction_method') == 'failed':\n                    failed_count += 1\n                    if failed_count <= 3:\n                        print(f'\\\\nFailed PDF: {doc.get(\\\"id\\\")}')\n                        print(f'  URL: {url[:80]}...')\n                        print(f'  Error: {doc.get(\\\"extraction_error\\\", \\\"Unknown\\\")}')\n    \n    print(f'\\\\nTotal PDFs in extraction_completed: {pdf_count}')\n    print(f'Failed PDFs: {failed_count}')\n    \n    await r.aclose()\n\nasyncio.run(find_pdf_documents())\n\")", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\nimport json\n\nasync def check_extracted_document():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    # Get the most recently extracted document\n    doc_json = await r.lindex('extraction_completed_queue', 0)\n    if doc_json:\n        doc = json.loads(doc_json)\n        \n        print(f'Document ID: {doc.get(\\\"id\\\")}')\n        print(f'URL: {doc.get(\\\"url\\\", \\\"\\\")[:80]}...')\n        print(f'Extraction method: {doc.get(\\\"extraction_method\\\")}')\n        print(f'Processing time: {doc.get(\\\"extraction_processing_time\\\", 0):.2f}s')\n        \n        # Check extraction quality\n        text = doc.get('extracted_text', '')\n        markdown = doc.get('extracted_markdown', '')\n        \n        print(f'\\\\nExtraction statistics:')\n        stats = doc.get('extraction_statistics', {})\n        print(f'  Total pages: {stats.get(\\\"total_pages\\\", \\\"N/A\\\")}')\n        print(f'  Total images: {stats.get(\\\"total_images\\\", \\\"N/A\\\")}')\n        print(f'  Total tables: {stats.get(\\\"total_tables\\\", \\\"N/A\\\")}')\n        print(f'  Has CJK: {stats.get(\\\"has_cjk\\\", \\\"N/A\\\")}')\n        print(f'  Method used: {stats.get(\\\"extraction_method\\\", \\\"N/A\\\")}')\n        \n        print(f'\\\\nText length: {len(text)} characters')\n        print(f'Markdown length: {len(markdown)} characters')\n        \n        # Show first 500 characters to check Japanese extraction\n        if text:\n            print(f'\\\\nFirst 500 characters of extracted text:')\n            print('-' * 50)\n            print(text[:500])\n            print('-' * 50)\n            \n        # Check if assets were stored\n        if doc.get('asset_refs'):\n            print(f'\\\\nAssets stored: {doc.get(\\\"asset_refs\\\")}')\n        \n    await r.aclose()\n\nasyncio.run(check_extracted_document())\n\")", "Bash(REDIS_URL=redis://localhost:6380 PDF_API_URL=http://localhost:8015 uv run python3 process_documents_simple.py --max 10)", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\nimport json\n\nasync def check_extracted_quality():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    # Get the most recently extracted document\n    doc_json = await r.lindex('extraction_completed_queue', 0)\n    if doc_json:\n        doc = json.loads(doc_json)\n        \n        print(f'Document ID: {doc.get(\\\"id\\\")}')\n        print(f'URL: {doc.get(\\\"url\\\", \\\"\\\")[:80]}...')\n        print(f'Extraction method: {doc.get(\\\"extraction_method\\\")}')\n        print(f'Processing time: {doc.get(\\\"extraction_processing_time\\\", 0):.2f}s')\n        \n        # Check extraction quality\n        text = doc.get('extracted_text', '')\n        \n        print(f'\\\\nExtraction quality:')\n        print(f'  Text length: {len(text)} characters')\n        \n        # Check for Japanese characters\n        has_japanese = any(0x3000 <= ord(c) <= 0x9FFF for c in text[:1000])\n        print(f'  Contains Japanese: {has_japanese}')\n        \n        # Show first 300 characters\n        if text:\n            print(f'\\\\nFirst 300 characters:')\n            print('-' * 50)\n            print(text[:300])\n            print('-' * 50)\n        \n        # Check tables and images\n        stats = doc.get('extraction_statistics', {})\n        print(f'\\\\nStatistics:')\n        for key, value in stats.items():\n            print(f'  {key}: {value}')\n    \n    await r.aclose()\n\nasyncio.run(check_extracted_quality())\n\")", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\n\nasync def check_queue_status():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    discovery_size = await r.llen('document_discovery_queue')\n    completed_size = await r.llen('extraction_completed_queue')\n    \n    print(f'Documents remaining to process: {discovery_size}')\n    print(f'Documents completed: {completed_size}')\n    \n    await r.aclose()\n\nasyncio.run(check_queue_status())\n\")", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\nimport json\n\nasync def analyze_completed_queue():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    completed_size = await r.llen('extraction_completed_queue')\n    print(f'Total documents in extraction_completed_queue: {completed_size}')\n    \n    # Analyze different types and quality\n    stats = {\n        'total': 0,\n        'pdf_documents': 0,\n        'non_pdf_documents': 0,\n        'recent_good_extractions': 0,\n        'empty_extractions': 0,\n        'skipped_documents': 0,\n        'mineru_extractions': 0,\n        'basic_extractions': 0,\n        'old_extractions': 0\n    }\n    \n    sample_docs = []\n    \n    # Check last 100 documents for analysis\n    for i in range(min(100, completed_size)):\n        doc_json = await r.lindex('extraction_completed_queue', i)\n        if doc_json:\n            doc = json.loads(doc_json)\n            stats['total'] += 1\n            \n            url = doc.get('url', '')\n            method = doc.get('extraction_method', 'unknown')\n            text = doc.get('extracted_text', '')\n            processing_time = doc.get('extraction_processing_time', 0)\n            \n            if url.lower().endswith('.pdf'):\n                stats['pdf_documents'] += 1\n            else:\n                stats['non_pdf_documents'] += 1\n            \n            if method == 'skipped':\n                stats['skipped_documents'] += 1\n            elif method == 'mineru':\n                stats['mineru_extractions'] += 1\n                # Check if it's a recent good extraction\n                if len(text) > 100 and processing_time > 5:\n                    stats['recent_good_extractions'] += 1\n                elif len(text) < 100:\n                    stats['empty_extractions'] += 1\n            elif method in ['basic', 'pdftotext']:\n                stats['basic_extractions'] += 1\n            \n            # Sample some documents for inspection\n            if i < 10:\n                sample_docs.append({\n                    'id': doc.get('id', 'unknown'),\n                    'url': url[:50] + '...' if len(url) > 50 else url,\n                    'method': method,\n                    'text_length': len(text),\n                    'processing_time': processing_time,\n                    'has_japanese': any(0x3000 <= ord(c) <= 0x9FFF for c in text[:200]) if text else False\n                })\n    \n    print(f'\\\\n=== Analysis of Last 100 Documents ===')\n    for key, value in stats.items():\n        print(f'{key}: {value}')\n    \n    print(f'\\\\n=== Sample Documents (First 10) ===')\n    for i, doc in enumerate(sample_docs, 1):\n        print(f'{i}. {doc[\\\"id\\\"]}')\n        print(f'   URL: {doc[\\\"url\\\"]}')\n        print(f'   Method: {doc[\\\"method\\\"]}')\n        print(f'   Text length: {doc[\\\"text_length\\\"]}')\n        print(f'   Processing time: {doc[\\\"processing_time\\\"]:.2f}s')\n        print(f'   Has Japanese: {doc[\\\"has_japanese\\\"]}')\n        print()\n    \n    await r.aclose()\n\nasyncio.run(analyze_completed_queue())\n\")", "Bash(REDIS_URL=redis://localhost:6380 uv run python3 -c \"\nimport redis.asyncio as redis\nimport asyncio\n\nasync def check_current_status():\n    r = await redis.from_url('redis://localhost:6380', decode_responses=True)\n    \n    discovery_size = await r.llen('document_discovery_queue')\n    completed_size = await r.llen('extraction_completed_queue')\n    nonpdf_size = await r.llen('non_pdf_completed_queue')\n    \n    print(f'=== Current Processing Status ===')\n    print(f'PDFs remaining to process: {discovery_size}')\n    print(f'PDFs successfully extracted: {completed_size}')\n    print(f'Non-PDF documents (skipped): {nonpdf_size}')\n    print(f'')\n    print(f'Total PDF documents: {discovery_size + completed_size}')\n    print(f'Progress: {completed_size}/{discovery_size + completed_size} ({completed_size/(discovery_size + completed_size)*100:.1f}%)')\n    \n    await r.aclose()\n\nasyncio.run(check_current_status())\n\")", "Bash(npm install:*)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "Bash(uv add:*)", "<PERSON><PERSON>(echo:*)", "Bash(ss:*)", "Bash(fuser:*)", "Bash(sudo lsof:*)", "Bash(npx next:*)", "<PERSON><PERSON>(sudo netstat:*)", "Bash(direnv reload:*)", "Bash(GEMINI_API_KEY=\"AIzaSyARoHIYbJ44QpoDejwU0_JpnjPaiXAOf_Y\" docker compose up --force-recreate -d market-analyzer)", "Bash(DEEPSEEK_API_KEY=\"***********************************\" docker compose up --build -d market-analyzer)", "Bash(REDIS_URL=\"redis://localhost:6381\" ASSET_STORAGE_PATH=\"/tmp/extracted_assets\" MINERU_SERVICE_URL=\"http://localhost:30000\" uv run python3 services/pdf_extractor/queue_based_extractor.py)", "Bash(for queue in document_discovery_queue extraction_completed_queue non_pdf_completed_queue extraction_retry_queue extraction_failed_dlq)", "Bash(do echo -n \"$queue: \")", "Bash(done)", "Bash(GEMINI_API_KEY=AIzaSyATRylvcqJxMIznvjh02uR81TeSZ5R7MgU docker-compose up -d flash-preprocessor pro-analyzer)", "Bash(# Move one document from extraction_completed to preprocessing manually\ndocker exec omotesamba-redis redis-cli RPOPLPUSH extraction_completed_queue preprocessing_queue > /dev/null\n\n# Check if pro-analyzer will process it\necho \"Moved 1 document to preprocessing_queue\"\nsleep 5\n\n# Check queues\necho -e \"\\n=== Queue Status After Manual Move ===\"\nfor queue in extraction_completed_queue preprocessing_queue analysis_queue storage_completed_queue; do\n  count=$(docker exec omotesamba-redis redis-cli --raw LLEN $queue)\n  echo \"$queue: $count documents\"\ndone)", "Bash(--network omotesamba_default )", "Bash(-p 8004:8004 )", "Bash(-e REDIS_HOST=redis )", "Bash(-e REDIS_PORT=6379 )", "Bash(-e POSTGRES_HOST=timescaledb )", "Bash(-e POSTGRES_PORT=5432 )", "Bash(-e POSTGRES_DB=omotesamba )", "Bash(-e POSTGRES_USER=omotesamba )", "Bash(-e POSTGRES_PASSWORD=omotesamba )", "Bash(-e DB_HOST=timescaledb )", "Bash(-e DB_PORT=5432 )", "Bash(-e DB_NAME=omotesamba )", "Bash(-e DB_USER=omotesamba )", "Bash(-e DB_PASSWORD=omotesamba )", "Bash(-e PROMPT_MANAGER_PORT=8004 )", "<PERSON><PERSON>(omotesamba-prompt-manager:latest )", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(sed:*)", "Bash(# Move the wrongly placed data to correct location\nmv real-estate/data/properties_real data/\n# Remove the wrongly created nested folder\nrm -rf real-estate/)", "WebFetch(domain:api-docs.deepseek.com)", "Bash(for queue in extraction_queue preprocessing_queue validation_queue extraction_failed_dlq extraction_quality_dlq market_analysis_failed_dlq market_intelligence_queue alert_queue)", "Bash(do docker exec omotesamba-redis redis-cli -n 0 DEL \"$queue\")"], "deny": []}, "enableAllProjectMcpServers": false}